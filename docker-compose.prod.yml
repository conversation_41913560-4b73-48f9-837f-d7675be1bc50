services:
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "4545:4545"
    env_file:
      - ./frontend/.env.production
    environment:
      - NEXT_PUBLIC_GRAPHQL_URL=https://cryptodo.dsserv.de/graphql
      - NODE_ENV=production
    depends_on:
      - backend
    networks:
      - cryptodo-network
    restart: unless-stopped

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: production
    ports:
      - "4544:4544"
    env_file:
      - ./backend/.env.production
    environment:
      - MONGODB_URI=mongodb://mongodb:27017/cryptodo
      - FRONTEND_URL=https://cryptodo.dsserv.de
      - NODE_ENV=production
    depends_on:
      - mongodb
    networks:
      - cryptodo-network
    restart: unless-stopped

  mongodb:
    image: mongo:7
    ports:
      - "27018:27017"
    volumes:
      - mongodb_data:/data/db
    networks:
      - cryptodo-network
    restart: unless-stopped
    environment:
      - MONGO_INITDB_DATABASE=cryptodo

volumes:
  mongodb_data:

networks:
  cryptodo-network:
    driver: bridge
