# Google OAuth Setup Guide

## Step 1: Create Google Cloud Project

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Click "Select a project" → "New Project"
3. Enter project name (e.g., "Cryptodo App")
4. Click "Create"

## Step 2: Enable Google APIs

1. In the Google Cloud Console, go to "APIs & Services" → "Library"
2. Search for "Google+ API" and click on it
3. Click "Enable"
4. Also enable "Google Identity" API if available

## Step 3: Create OAuth 2.0 Credentials

1. Go to "APIs & Services" → "Credentials"
2. Click "Create Credentials" → "OAuth client ID"
3. If prompted, configure the OAuth consent screen:
   - Choose "External" user type
   - Fill in required fields:
     - App name: "Cryptodo"
     - User support email: your email
     - Developer contact information: your email
   - Add scopes: email, profile, openid
   - Add test users if needed
4. Choose "Web application" as application type
5. Configure the OAuth client:
   - Name: "Cryptodo Web Client"
   - Authorized JavaScript origins:
     - `http://localhost:4545` (for development)
     - `https://yourdomain.com` (for production)
   - Authorized redirect URIs:
     - `http://localhost:4545` (for development)
     - `https://yourdomain.com` (for production)
6. Click "Create"
7. Copy the "Client ID" (it looks like: `123456789-abcdefghijklmnop.apps.googleusercontent.com`)

## Step 4: Update Environment Variables

1. Open `frontend/.env.local`
2. Replace `your-actual-google-client-id-here` with your actual Client ID:
   ```
   NEXT_PUBLIC_GOOGLE_CLIENT_ID=123456789-abcdefghijklmnop.apps.googleusercontent.com
   ```
3. Save the file
4. Restart your frontend development server:
   ```bash
   cd frontend
   pnpm run dev
   ```

## Step 5: Test Google OAuth

1. Open your app at `http://localhost:4545`
2. Click "Sign In"
3. You should now see the Google Sign-In button
4. Click it to test the authentication flow

## Troubleshooting

### Error: "The OAuth client was not found"
- Make sure you copied the correct Client ID
- Verify the Client ID is set in the environment variable
- Restart the development server after changing environment variables

### Error: "redirect_uri_mismatch"
- Make sure `http://localhost:4545` is added to authorized origins in Google Cloud Console
- Check that the port number matches (4545)

### Error: "access_blocked"
- Make sure your email is added as a test user in the OAuth consent screen
- Or publish your app (for production use)

### Google Sign-In button not showing
- Check browser console for errors
- Verify the Client ID is not the placeholder value
- Make sure the environment variable is loaded correctly

## Production Setup

For production deployment:

1. Add your production domain to authorized origins:
   - `https://yourdomain.com`
2. Update the OAuth consent screen for production use
3. Set the production Client ID in your production environment variables
4. Consider creating separate OAuth clients for development and production

## Security Notes

- Never commit your Client ID to version control if it's sensitive
- Use different OAuth clients for development and production
- Regularly review and rotate credentials
- Monitor OAuth usage in Google Cloud Console
