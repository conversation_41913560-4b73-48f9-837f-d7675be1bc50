# CryptoDo Deployment Guide for dsserv.de

This guide explains how to deploy the CryptoDo application to your dsserv.de server.

## Prerequisites

- Docker and Docker Compose installed on dsserv.de server
- Access to dsserv.de server via SSH
- Domain dsserv.de pointing to your server
- Ports 4544 and 4545 available on the server

## Quick Deployment

1. **Upload the project to your server:**
   ```bash
   scp -r . <EMAIL>:/path/to/cryptodo/
   ```

2. **SSH into your server:**
   ```bash
   ssh <EMAIL>
   cd /path/to/cryptodo/
   ```

3. **Run the deployment script:**
   ```bash
   ./deploy-server.sh
   ```

4. **Access your application:**
   - Frontend: https://dsserv.de:4545
   - Backend API: https://dsserv.de:4544/graphql

## Manual Deployment

If you prefer manual deployment:

```bash
# Build and start production containers
docker compose -f docker-compose.prod.yml up -d --build

# Check container status
docker compose -f docker-compose.prod.yml ps

# View logs
docker compose -f docker-compose.prod.yml logs -f
```

## Configuration Details

### Environment Variables

The production deployment uses the following configuration:

**Frontend:**
- `NEXT_PUBLIC_GRAPHQL_URL=https://dsserv.de:4544/graphql`
- `NODE_ENV=production`

**Backend:**
- `MONGODB_URI=mongodb://mongodb:27017/cryptodo`
- `FRONTEND_URL=https://dsserv.de:4545`
- `NODE_ENV=production`
- `PORT=4544`

### CORS Configuration

The backend automatically allows requests from:
- `https://dsserv.de:4545` (production frontend)
- `http://dsserv.de:4545` (fallback without SSL)
- `https://dsserv.de` (main domain)
- `http://dsserv.de` (fallback without SSL)
- `http://localhost:4545` (local development)

### Ports

- **Frontend (Next.js):** Port 4545
- **Backend (NestJS):** Port 4544
- **MongoDB:** Port 27018 (mapped from internal 27017)

## SSL/HTTPS Setup (Optional)

For production use, consider setting up SSL certificates:

1. **Using Let's Encrypt with Certbot:**
   ```bash
   sudo apt install certbot
   sudo certbot certonly --standalone -d dsserv.de
   ```

2. **Using the provided nginx configuration:**
   - Copy `nginx.conf` to your nginx sites directory
   - Update SSL certificate paths
   - Restart nginx

3. **Update environment variables to use HTTPS:**
   - The production configuration already uses HTTPS URLs
   - Ensure your SSL certificates are properly configured

## Firewall Configuration

Make sure the following ports are open on your server:

```bash
# Allow HTTP and HTTPS
sudo ufw allow 80
sudo ufw allow 443

# Allow application ports
sudo ufw allow 4544
sudo ufw allow 4545

# Allow MongoDB (if accessing externally)
sudo ufw allow 27018
```

## Monitoring and Maintenance

### View Logs
```bash
# All services
docker compose -f docker-compose.prod.yml logs -f

# Specific service
docker compose -f docker-compose.prod.yml logs -f frontend
docker compose -f docker-compose.prod.yml logs -f backend
docker compose -f docker-compose.prod.yml logs -f mongodb
```

### Restart Services
```bash
# Restart all services
docker compose -f docker-compose.prod.yml restart

# Restart specific service
docker compose -f docker-compose.prod.yml restart frontend
```

### Update Application
```bash
# Pull latest changes
git pull

# Rebuild and restart
docker compose -f docker-compose.prod.yml up -d --build
```

### Backup Database
```bash
# Create MongoDB backup
docker exec cryptodo-mongodb-1 mongodump --db cryptodo --out /data/backup

# Copy backup from container
docker cp cryptodo-mongodb-1:/data/backup ./mongodb-backup-$(date +%Y%m%d)
```

### Restore Database
```bash
# Copy backup to container
docker cp ./mongodb-backup-20231201 cryptodo-mongodb-1:/data/restore

# Restore database
docker exec cryptodo-mongodb-1 mongorestore --db cryptodo /data/restore/cryptodo
```

## Troubleshooting

### Common Issues

1. **CORS Errors:**
   - Check that the frontend URL in backend environment matches your domain
   - Verify the backend CORS configuration includes your domain

2. **Connection Refused:**
   - Ensure all containers are running: `docker compose -f docker-compose.prod.yml ps`
   - Check firewall settings
   - Verify port availability

3. **Database Connection Issues:**
   - Check MongoDB container status
   - Verify MongoDB URI in backend environment
   - Check MongoDB logs: `docker compose -f docker-compose.prod.yml logs mongodb`

4. **Build Failures:**
   - Clear Docker cache: `docker system prune -a`
   - Check available disk space
   - Review build logs for specific errors

### Health Checks

```bash
# Check if frontend is responding
curl -I https://dsserv.de:4545

# Check if backend API is responding
curl -I https://dsserv.de:4544/graphql

# Check container health
docker compose -f docker-compose.prod.yml ps
```

## Security Considerations

1. **Use HTTPS in production**
2. **Configure proper firewall rules**
3. **Regularly update Docker images**
4. **Monitor application logs**
5. **Backup database regularly**
6. **Use strong MongoDB authentication (if exposing externally)**

## Support

If you encounter issues:
1. Check the logs first
2. Verify all environment variables are correct
3. Ensure all required ports are open
4. Check Docker and Docker Compose versions
5. Review the troubleshooting section above
