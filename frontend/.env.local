NEXT_PUBLIC_GRAPHQL_URL=http://localhost:4544/graphql
# Replace this with your actual Google OAuth Client ID from Google Cloud Console
NEXT_PUBLIC_GOOGLE_CLIENT_ID=your-actual-google-client-id-here
NODE_ENV=development

# Instructions:
# 1. Go to https://console.cloud.google.com/
# 2. Create a new project or select existing
# 3. Enable Google+ API
# 4. Go to APIs & Services > Credentials
# 5. Create OAuth client ID (Web application)
# 6. Add http://localhost:4545 to authorized origins
# 7. Copy the Client ID and replace the value above
