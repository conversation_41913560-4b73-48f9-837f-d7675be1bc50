import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { gql, useMutation, useQuery } from '@apollo/client';
import Cookies from 'js-cookie';

interface User {
  _id: string;
  email: string;
  name: string;
  avatar?: string;
  language?: string;
  oauthProviders: string[];
  isActive: boolean;
  emailVerified: boolean;
  createdAt: string;
  updatedAt: string;
}

interface AuthContextType {
  user: User | null;
  loading: boolean;
  login: (email: string, password: string) => Promise<void>;
  register: (email: string, password: string, name: string) => Promise<void>;
  oauthLogin: (provider: string, providerId: string, email: string, name: string, avatar?: string) => Promise<void>;
  logout: () => void;
  updateProfile: (data: any) => Promise<void>;
  changePassword: (currentPassword: string, newPassword: string) => Promise<void>;
  requestPasswordReset: (email: string) => Promise<void>;
  resetPassword: (token: string, newPassword: string) => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

const ME_QUERY = gql`
  query Me {
    me {
      _id
      email
      name
      avatar
      language
      oauthProviders
      isActive
      emailVerified
      createdAt
      updatedAt
    }
  }
`;

const LOGIN_MUTATION = gql`
  mutation Login($loginInput: LoginInput!) {
    login(loginInput: $loginInput) {
      accessToken
      user {
        _id
        email
        name
        avatar
        language
        oauthProviders
        isActive
        emailVerified
        createdAt
        updatedAt
      }
    }
  }
`;

const REGISTER_MUTATION = gql`
  mutation Register($registerInput: RegisterInput!) {
    register(registerInput: $registerInput) {
      accessToken
      user {
        _id
        email
        name
        avatar
        language
        oauthProviders
        isActive
        emailVerified
        createdAt
        updatedAt
      }
    }
  }
`;

const OAUTH_LOGIN_MUTATION = gql`
  mutation OAuthLogin($oauthInput: OAuthLoginInput!) {
    oauthLogin(oauthInput: $oauthInput) {
      accessToken
      user {
        _id
        email
        name
        avatar
        language
        oauthProviders
        isActive
        emailVerified
        createdAt
        updatedAt
      }
    }
  }
`;

const UPDATE_PROFILE_MUTATION = gql`
  mutation UpdateProfile($updateUserInput: UpdateUserInput!) {
    updateProfile(updateUserInput: $updateUserInput) {
      _id
      email
      name
      avatar
      language
      oauthProviders
      isActive
      emailVerified
      createdAt
      updatedAt
    }
  }
`;

const CHANGE_PASSWORD_MUTATION = gql`
  mutation ChangePassword($changePasswordInput: ChangePasswordInput!) {
    changePassword(changePasswordInput: $changePasswordInput) {
      message
    }
  }
`;

const REQUEST_PASSWORD_RESET_MUTATION = gql`
  mutation RequestPasswordReset($requestPasswordResetInput: RequestPasswordResetInput!) {
    requestPasswordReset(requestPasswordResetInput: $requestPasswordResetInput) {
      message
    }
  }
`;

const RESET_PASSWORD_MUTATION = gql`
  mutation ResetPassword($resetPasswordInput: ResetPasswordInput!) {
    resetPassword(resetPasswordInput: $resetPasswordInput) {
      message
    }
  }
`;

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  const { data: meData, loading: meLoading, refetch: refetchMe } = useQuery(ME_QUERY, {
    skip: !Cookies.get('token'),
    errorPolicy: 'ignore',
  });

  const [loginMutation] = useMutation(LOGIN_MUTATION);
  const [registerMutation] = useMutation(REGISTER_MUTATION);
  const [oauthLoginMutation] = useMutation(OAUTH_LOGIN_MUTATION);
  const [updateProfileMutation] = useMutation(UPDATE_PROFILE_MUTATION);
  const [changePasswordMutation] = useMutation(CHANGE_PASSWORD_MUTATION);
  const [requestPasswordResetMutation] = useMutation(REQUEST_PASSWORD_RESET_MUTATION);
  const [resetPasswordMutation] = useMutation(RESET_PASSWORD_MUTATION);

  useEffect(() => {
    if (meData?.me) {
      setUser(meData.me);
    }
    setLoading(meLoading);
  }, [meData, meLoading]);

  const login = async (email: string, password: string) => {
    try {
      const { data } = await loginMutation({
        variables: {
          loginInput: { email, password },
        },
      });

      if (data?.login) {
        Cookies.set('token', data.login.accessToken, { expires: 7 });
        setUser(data.login.user);
      }
    } catch (error) {
      throw error;
    }
  };

  const register = async (email: string, password: string, name: string) => {
    try {
      const { data } = await registerMutation({
        variables: {
          registerInput: { email, password, name },
        },
      });

      if (data?.register) {
        Cookies.set('token', data.register.accessToken, { expires: 7 });
        setUser(data.register.user);
      }
    } catch (error) {
      throw error;
    }
  };

  const oauthLogin = async (provider: string, providerId: string, email: string, name: string, avatar?: string) => {
    try {
      const { data } = await oauthLoginMutation({
        variables: {
          oauthInput: { provider, providerId, email, name, avatar },
        },
      });

      if (data?.oauthLogin) {
        Cookies.set('token', data.oauthLogin.accessToken, { expires: 7 });
        setUser(data.oauthLogin.user);
      }
    } catch (error) {
      throw error;
    }
  };

  const logout = () => {
    Cookies.remove('token');
    setUser(null);
    // Clear Apollo cache
    window.location.reload();
  };

  const updateProfile = async (updateData: any) => {
    try {
      const { data } = await updateProfileMutation({
        variables: {
          updateUserInput: updateData,
        },
      });

      if (data?.updateProfile) {
        setUser(data.updateProfile);
      }
    } catch (error) {
      throw error;
    }
  };

  const changePassword = async (currentPassword: string, newPassword: string) => {
    try {
      await changePasswordMutation({
        variables: {
          changePasswordInput: { currentPassword, newPassword },
        },
      });
    } catch (error) {
      throw error;
    }
  };

  const requestPasswordReset = async (email: string) => {
    try {
      await requestPasswordResetMutation({
        variables: {
          requestPasswordResetInput: { email },
        },
      });
    } catch (error) {
      throw error;
    }
  };

  const resetPassword = async (token: string, newPassword: string) => {
    try {
      await resetPasswordMutation({
        variables: {
          resetPasswordInput: { token, newPassword },
        },
      });
    } catch (error) {
      throw error;
    }
  };

  const value = {
    user,
    loading,
    login,
    register,
    oauthLogin,
    logout,
    updateProfile,
    changePassword,
    requestPasswordReset,
    resetPassword,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
