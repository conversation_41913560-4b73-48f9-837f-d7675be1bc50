// Authentication Modal Styles
.auth-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.auth-modal {
  background: white;
  border-radius: 12px;
  padding: 0;
  width: 90%;
  max-width: 400px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  &__header {
    padding: 24px 24px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #e5e7eb;
    margin-bottom: 24px;

    h2 {
      margin: 0;
      font-size: 24px;
      font-weight: 600;
      color: #111827;
    }
  }

  &__close {
    background: none;
    border: none;
    padding: 8px;
    cursor: pointer;
    color: #6b7280;
    border-radius: 6px;
    transition: all 0.2s;

    &:hover {
      background-color: #f3f4f6;
      color: #374151;
    }
  }

  &__content {
    padding: 0 24px 24px;
  }

  &__error {
    background-color: #fef2f2;
    border: 1px solid #fecaca;
    color: #dc2626;
    padding: 12px;
    border-radius: 8px;
    margin-bottom: 16px;
    font-size: 14px;
  }

  &__message {
    background-color: #f0fdf4;
    border: 1px solid #bbf7d0;
    color: #166534;
    padding: 12px;
    border-radius: 8px;
    margin-bottom: 16px;
    font-size: 14px;
  }

  &__oauth {
    margin: 24px 0;
  }

  &__google-disabled {
    text-align: center;
    padding: 16px;
    background-color: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    color: #6b7280;

    p {
      margin: 4px 0;
      font-size: 14px;

      &:first-child {
        font-weight: 500;
        color: #374151;
      }
    }
  }

  &__divider {
    position: relative;
    text-align: center;
    margin: 20px 0;

    &::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 0;
      right: 0;
      height: 1px;
      background-color: #e5e7eb;
    }

    span {
      background: white;
      padding: 0 16px;
      color: #6b7280;
      font-size: 14px;
    }
  }

  &__footer {
    margin-top: 24px;
    text-align: center;

    p {
      margin: 8px 0;
      color: #6b7280;
      font-size: 14px;
    }
  }

  &__link {
    color: var(--primary-color);
    background: none;
    border: none;
    cursor: pointer;
    text-decoration: underline;
    font-size: 14px;

    &:hover {
      color: #0056b3;
    }
  }
}

.auth-form {
  &__field {
    margin-bottom: 16px;

    label {
      display: block;
      margin-bottom: 6px;
      font-weight: 500;
      color: #374151;
      font-size: 14px;
    }

    input {
      width: 100%;
      padding: 12px;
      border: 1px solid #d1d5db;
      border-radius: 8px;
      font-size: 16px;
      transition: border-color 0.2s;

      &:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(0, 121, 191, 0.1);
      }
    }
  }

  &__error {
    color: #dc2626;
    font-size: 12px;
    margin-top: 4px;
  }

  &__submit {
    width: 100%;
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 12px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;

    &:hover:not(:disabled) {
      background-color: #0056b3;
    }

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
  }
}

// User Settings Modal Styles
.settings-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.settings-modal {
  background: white;
  border-radius: 12px;
  padding: 0;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  &__header {
    padding: 24px 24px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #e5e7eb;

    h2 {
      margin: 0;
      font-size: 24px;
      font-weight: 600;
      color: #111827;
    }
  }

  &__close {
    background: none;
    border: none;
    padding: 8px;
    cursor: pointer;
    color: #6b7280;
    border-radius: 6px;
    transition: all 0.2s;

    &:hover {
      background-color: #f3f4f6;
      color: #374151;
    }
  }

  &__tabs {
    display: flex;
    border-bottom: 1px solid #e5e7eb;
  }

  &__tab {
    flex: 1;
    padding: 16px;
    background: none;
    border: none;
    cursor: pointer;
    font-size: 16px;
    font-weight: 500;
    color: #6b7280;
    border-bottom: 2px solid transparent;
    transition: all 0.2s;

    &.active {
      color: var(--primary-color);
      border-bottom-color: var(--primary-color);
    }

    &:hover:not(.active) {
      color: #374151;
    }
  }

  &__content {
    padding: 24px;
  }

  &__error {
    background-color: #fef2f2;
    border: 1px solid #fecaca;
    color: #dc2626;
    padding: 12px;
    border-radius: 8px;
    margin-bottom: 16px;
    font-size: 14px;
  }

  &__message {
    background-color: #f0fdf4;
    border: 1px solid #bbf7d0;
    color: #166534;
    padding: 12px;
    border-radius: 8px;
    margin-bottom: 16px;
    font-size: 14px;
  }
}

.settings-form {
  &__field {
    margin-bottom: 16px;

    label {
      display: block;
      margin-bottom: 6px;
      font-weight: 500;
      color: #374151;
      font-size: 14px;
    }

    input, select {
      width: 100%;
      padding: 12px;
      border: 1px solid #d1d5db;
      border-radius: 8px;
      font-size: 16px;
      transition: border-color 0.2s;

      &:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(0, 121, 191, 0.1);
      }
    }
  }

  &__error {
    color: #dc2626;
    font-size: 12px;
    margin-top: 4px;
  }

  &__submit {
    width: 100%;
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 12px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;

    &:hover:not(:disabled) {
      background-color: #0056b3;
    }

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
  }

  &__oauth-info {
    margin: 24px 0;
    padding: 16px;
    background-color: #f9fafb;
    border-radius: 8px;

    h4 {
      margin: 0 0 12px 0;
      font-size: 16px;
      color: #374151;
    }

    .oauth-providers {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }

    .oauth-provider {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 8px;
      background: white;
      border-radius: 6px;
      border: 1px solid #e5e7eb;

      &__icon {
        font-size: 16px;
      }
    }

    .no-oauth {
      color: #6b7280;
      font-style: italic;
      margin: 0;
    }
  }
}

// Board Selection Modal Styles
.board-selection-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.board-selection-modal {
  background: white;
  border-radius: 12px;
  padding: 0;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  &__header {
    padding: 24px 24px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #e5e7eb;
    margin-bottom: 24px;

    h2 {
      margin: 0;
      font-size: 24px;
      font-weight: 600;
      color: #111827;
    }
  }

  &__close {
    background: none;
    border: none;
    padding: 8px;
    cursor: pointer;
    color: #6b7280;
    border-radius: 6px;
    transition: all 0.2s;

    &:hover {
      background-color: #f3f4f6;
      color: #374151;
    }
  }

  &__content {
    padding: 0 24px 24px;
  }

  &__error {
    background-color: #fef2f2;
    border: 1px solid #fecaca;
    color: #dc2626;
    padding: 12px;
    border-radius: 8px;
    margin-bottom: 16px;
    font-size: 14px;
  }

  &__actions {
    margin-bottom: 24px;
  }

  &__create-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 12px 16px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;

    &:hover {
      background-color: #0056b3;
    }
  }
}

.board-list {
  &__loading {
    text-align: center;
    padding: 40px;
    color: #6b7280;
  }

  &__empty {
    text-align: center;
    padding: 40px;
    color: #6b7280;

    p {
      margin: 0;
    }
  }
}

.board-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  margin-bottom: 12px;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    border-color: var(--primary-color);
    box-shadow: 0 2px 4px rgba(0, 121, 191, 0.1);
  }

  &__info {
    flex: 1;
  }

  &__name {
    margin: 0 0 4px 0;
    font-size: 18px;
    font-weight: 500;
    color: #111827;
  }

  &__date {
    margin: 0;
    font-size: 14px;
    color: #6b7280;
  }

  &__arrow {
    color: #6b7280;
  }
}

.create-board-form {
  &__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24px;

    h3 {
      margin: 0;
      font-size: 20px;
      font-weight: 600;
      color: #111827;
    }
  }

  &__back {
    display: flex;
    align-items: center;
    gap: 4px;
    background: none;
    border: none;
    color: #6b7280;
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: all 0.2s;

    &:hover {
      background-color: #f3f4f6;
      color: #374151;
    }
  }

  &__form {
    .create-board-form__field {
      margin-bottom: 16px;

      label {
        display: block;
        margin-bottom: 6px;
        font-weight: 500;
        color: #374151;
        font-size: 14px;
      }

      input {
        width: 100%;
        padding: 12px;
        border: 1px solid #d1d5db;
        border-radius: 8px;
        font-size: 16px;
        transition: border-color 0.2s;

        &:focus {
          outline: none;
          border-color: var(--primary-color);
          box-shadow: 0 0 0 3px rgba(0, 121, 191, 0.1);
        }
      }
    }

    .create-board-form__error {
      color: #dc2626;
      font-size: 12px;
      margin-top: 4px;
    }

    .create-board-form__submit {
      width: 100%;
      background-color: var(--primary-color);
      color: white;
      border: none;
      padding: 12px;
      border-radius: 8px;
      font-size: 16px;
      font-weight: 500;
      cursor: pointer;
      transition: background-color 0.2s;

      &:hover:not(:disabled) {
        background-color: #0056b3;
      }

      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
      }
    }
  }
}
