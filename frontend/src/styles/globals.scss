:root {
  --primary-color: #0079bf;
  --secondary-color: #ebecf0;
  --text-color: #172b4d;
  --light-text: #5e6c84;
  --border-color: #dfe1e6;
  --card-shadow: 0 1px 0 rgba(9, 30, 66, 0.25);
  --cryptodo-blue-background: #0079bf;
  --cryptodo-header-height: 40px;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen,
    Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  color: var(--text-color);
  background-color: var(--cryptodo-blue-background);
  height: 100%;
}

a {
  color: inherit;
  text-decoration: none;
}

button {
  cursor: pointer;
  border: none;
  background: none;
  font-family: inherit;

  &:focus {
    outline: none;
  }
}

.app-header {
  height: var(--cryptodo-header-height);
  background-color: rgba(0, 0, 0, 0.25);
  padding: 0 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: white;
  font-weight: bold;
  font-size: 18px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(5px);
  z-index: 20;

  &__logo {
    display: flex;
    align-items: center;
    gap: 16px;

    svg {
      margin-right: 8px;
    }
  }

  &__middle {
    display: flex;
    align-items: center;
    flex: 1;
    justify-content: center;
  }

  &__actions {
    display: flex;
    align-items: center;
    position: relative;
    gap: 12px;
  }

  &__menu-btn {
    color: white;
    padding: 6px;
    border-radius: 3px;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      background-color: rgba(255, 255, 255, 0.2);
    }
  }

  &__menu {
    position: absolute;
    top: 100%;
    right: 0;
    margin-top: 4px;
    background-color: white;
    border-radius: 3px;
    box-shadow: 0 8px 16px -4px rgba(9, 30, 66, 0.25);
    width: 200px;
    z-index: 30;
    overflow: hidden;
  }

  &__menu-item {
    width: 100%;
    padding: 8px 12px;
    text-align: left;
    display: flex;
    align-items: center;
    font-size: 14px;
    font-weight: normal;
    color: var(--text-color);
    background: none;
    border: none;
    cursor: pointer;
    border-bottom: 1px solid var(--border-color);

    svg {
      margin-right: 8px;
    }

    &:hover {
      background-color: rgba(9, 30, 66, 0.04);
    }

    &:last-child {
      border-bottom: none;
    }
  }

  &__importing {
    position: absolute;
    top: 100%;
    right: 0;
    margin-top: 4px;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 8px 12px;
    border-radius: 3px;
    font-size: 14px;
    font-weight: normal;
    animation: pulse 1.5s infinite;
  }

  // Authentication styles
  &__boards-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;

    &:hover {
      background: rgba(255, 255, 255, 0.2);
    }
  }

  &__login-btn {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;

    &:hover {
      background: rgba(255, 255, 255, 0.2);
      border-color: rgba(255, 255, 255, 0.5);
    }
  }

  &__user {
    position: relative;
  }

  &__user-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    transition: background-color 0.2s;

    &:hover {
      background: rgba(255, 255, 255, 0.2);
    }
  }

  &__avatar {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    object-fit: cover;
  }

  &__avatar-placeholder {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: 600;
  }

  &__user-name {
    font-weight: 500;
    max-width: 120px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  &__user-menu {
    position: absolute;
    top: 100%;
    right: 0;
    margin-top: 8px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    min-width: 180px;
    z-index: 30;
    overflow: hidden;
    border: 1px solid #e5e7eb;
  }

  &__user-menu-item {
    width: 100%;
    padding: 12px 16px;
    text-align: left;
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 14px;
    font-weight: 500;
    color: #374151;
    background: none;
    border: none;
    cursor: pointer;
    transition: background-color 0.2s;

    &:hover {
      background-color: #f9fafb;
    }

    &:not(:last-child) {
      border-bottom: 1px solid #f3f4f6;
    }

    svg {
      flex-shrink: 0;
    }
  }
}

@keyframes pulse {
  0% {
    opacity: 0.7;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.7;
  }
}

.board {
  display: flex;
  padding: 20px;
  overflow-x: auto;
  height: calc(100vh - var(--cryptodo-header-height));
  align-items: flex-start;
  background-image: url('https://images.unsplash.com/photo-1575516478880-7dfb1a114073?q=80&w=2072&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D');
  background-size: cover;
  background-position: center;
}

.swimlane-wrapper {
  margin: 0 8px;

  &.is-dragging {
    z-index: 10;
  }
}

.swimlane {
  flex: 0 0 272px;
  width: 272px;
  background-color: var(--secondary-color);
  border-radius: 3px;
  max-height: calc(100vh - var(--cryptodo-header-height) - 40px);
  display: flex;
  flex-direction: column;
  box-shadow: 0 1px 0 rgba(9, 30, 66, 0.25);

  &__header {
    padding: 10px 8px 8px 12px;
    font-weight: 600;
    font-size: 14px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: grab;

    &:active {
      cursor: grabbing;
    }

    h3 {
      cursor: pointer;
      margin: 0;
      padding: 4px;
      border-radius: 3px;
      display: flex;
      align-items: center;
      gap: 6px;

      &:hover {
        background-color: rgba(9, 30, 66, 0.08);
      }
    }

  &__sorted-indicator {
    display: inline-flex;
    align-items: center;
    color: var(--light-text);
    font-size: 12px;
    margin-left: 8px;
    background-color: rgba(0, 0, 0, 0.05);
    padding: 2px 6px;
    border-radius: 3px;
    gap: 4px;
  }

    .options-btn {
      color: #6b778c;
      padding: 6px;
      border-radius: 3px;

      &:hover {
        background-color: rgba(9, 30, 66, 0.08);
        color: #172b4d;
      }
    }
  }

  &__title-input {
    font-size: 14px;
    font-weight: 600;
    background-color: white;
    border: none;
    border-radius: 3px;
    padding: 4px 8px;
    width: 200px;
    box-shadow: inset 0 0 0 2px var(--primary-color);

    &:focus {
      outline: none;
    }
  }

  &__menu-container {
    position: relative;
  }

  &__menu {
    position: absolute;
    top: 100%;
    right: 0;
    width: 200px;
    background-color: white;
    border-radius: 3px;
    box-shadow: 0 8px 16px -4px rgba(9, 30, 66, 0.25), 0 0 0 1px rgba(9, 30, 66, 0.08);
    z-index: 10;
    overflow: hidden;
  }

  &__menu-item {
    width: 100%;
    padding: 8px 12px;
    text-align: left;
    display: flex;
    align-items: center;
    font-size: 14px;
    color: var(--text-color);
    background: none;
    border: none;
    cursor: pointer;

    svg {
      margin-right: 8px;
    }

    &:hover {
      background-color: rgba(9, 30, 66, 0.04);
    }

    &--delete {
      color: #eb5a46;

      &:hover {
        background-color: #fbe1dd;
      }
    }
  }

  &__content {
    padding: 0 8px;
    flex: 1;
    overflow-y: auto;
    min-height: 10px;

    &::-webkit-scrollbar {
      width: 8px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(0, 0, 0, 0.1);
      border-radius: 4px;
    }
  }

  &__footer {
    padding: 8px;
  }
}

.card {
  background-color: white;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(9, 30, 66, 0.15);
  padding: 10px;
  margin-bottom: 10px;
  cursor: pointer;
  position: relative;
  transition: transform 0.2s ease, box-shadow 0.2s ease;

  &:hover {
    background-color: #f9f9f9;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(9, 30, 66, 0.2);
  }

  &__header {
    display: flex;
    flex-direction: column;
    margin-bottom: 4px;
  }

  &__priority {
    display: inline-block;
    font-size: 11px;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 3px;
    margin-bottom: 4px;
    align-self: flex-start;

    &--low {
      background-color: #e2f5e2;
      color: #1e7e34;
    }

    &--high {
      background-color: #fff3cd;
      color: #856404;
    }

    &--top {
      background-color: #f8d7da;
      color: #721c24;
    }
  }

  &__title {
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
    word-wrap: break-word;
  }

  &__footer {
    display: flex;
    margin-top: 8px;
    font-size: 12px;
    color: var(--light-text);
    min-height: 18px; /* Ensure consistent height even when empty */
  }

  &__comment-count,
  &__checklist-count {
    display: flex;
    align-items: center;
    margin-right: 8px;

    svg {
      margin-right: 4px;
      width: 14px;
      height: 14px;
    }
  }

  &__actions {
    position: absolute;
    top: 4px;
    right: 4px;
    display: flex;
    opacity: 0;
    transition: opacity 0.2s ease;
  }

  &:hover &__actions {
    opacity: 1;
  }

  &__edit-btn,
  &__delete-btn {
    padding: 4px;
    border-radius: 3px;
    background: none;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      background-color: rgba(9, 30, 66, 0.08);
    }
  }

  &__delete-btn {
    margin-right: 2px;

    &:hover {
      color: #eb5a46 !important;
    }
  }
}

.add-card-btn {
  display: flex;
  align-items: center;
  color: var(--light-text);
  padding: 8px;
  border-radius: 3px;
  width: 100%;

  &:hover {
    background-color: rgba(9, 30, 66, 0.08);
    color: var(--text-color);
  }

  svg {
    margin-right: 6px;
  }
}

.add-card-textarea {
  width: 100%;
  border: none;
  box-shadow: 0 1px 0 rgba(9, 30, 66, 0.25);
  resize: none;
  min-height: 54px;
  padding: 8px;
  border-radius: 3px;
  font-family: inherit;
  font-size: 14px;
  margin-bottom: 8px;

  &:focus {
    outline: none;
    box-shadow: inset 0 0 0 2px var(--primary-color);
  }
}

.add-card-actions {
  display: flex;
  align-items: center;

  button {
    margin-right: 8px;
    padding: 6px 12px;
    border-radius: 3px;
    font-weight: 400;
    font-size: 14px;

    &:first-child {
      background-color: var(--primary-color);
      color: white;

      &:hover {
        background-color: #026aa7;
      }
    }

    &:last-child {
      color: var(--light-text);

      &:hover {
        background-color: rgba(9, 30, 66, 0.08);
        color: var(--text-color);
      }
    }
  }
}

.add-list-btn {
  flex: 0 0 272px;
  margin: 0 8px;
  background-color: rgba(255, 255, 255, 0.24);
  border-radius: 3px;
  height: 40px;
  color: white;
  font-weight: 400;
  display: flex;
  align-items: center;
  padding: 0 12px;
  cursor: pointer;

  &:hover {
    background-color: rgba(255, 255, 255, 0.32);
  }

  svg {
    margin-right: 6px;
  }
}

.add-list-form {
  flex: 0 0 272px;
  margin: 0 8px;
  background-color: var(--secondary-color);
  border-radius: 3px;
  padding: 8px;
  box-shadow: 0 1px 0 rgba(9, 30, 66, 0.25);
}

.add-list-input {
  width: 100%;
  border: none;
  box-shadow: inset 0 0 0 2px var(--primary-color);
  border-radius: 3px;
  padding: 8px 12px;
  font-family: inherit;
  font-size: 14px;
  margin-bottom: 8px;

  &:focus {
    outline: none;
  }
}

.add-list-actions {
  display: flex;
  align-items: center;
}

.add-list-save-btn {
  background-color: var(--primary-color);
  color: white;
  padding: 6px 12px;
  border-radius: 3px;
  font-weight: 400;
  font-size: 14px;
  margin-right: 8px;
  border: none;
  cursor: pointer;

  &:hover {
    background-color: #026aa7;
  }

  &:disabled {
    background-color: rgba(9, 30, 66, 0.04);
    color: var(--light-text);
    cursor: not-allowed;
  }
}

.add-list-cancel-btn {
  padding: 6px;
  border-radius: 3px;
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--light-text);

  &:hover {
    background-color: rgba(9, 30, 66, 0.08);
    color: var(--text-color);
  }
}

.card-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;

  &__content {
    background-color: rgba(244, 245, 247, 0.95);
    backdrop-filter: blur(5px);
    border-radius: 3px;
    width: 768px;
    max-width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    padding: 0;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
  }

  &__header {
    position: relative;
    padding: 12px 80px 8px 56px; /* Increased right padding to make room for buttons */
    margin-bottom: 8px;
    min-height: 32px;
    display: flex;
    flex-direction: column;
  }

  &__title-container {
    display: flex;
    flex-direction: row;
    align-items: center;
    width: 100%;
    flex-wrap: wrap;
  }

  &__timestamp {
    font-size: 12px;
    color: var(--light-text);
    font-style: italic;
    white-space: nowrap;
    position: absolute;
    right: 100px; /* Position to the left of the menu buttons */
    top: 20px;
    z-index: 4;
  }

  &__title {
    font-size: 20px;
    font-weight: 600;
    line-height: 1.5;
    margin: 0;
    flex: 1;
  }

  &__title-input {
    width: 100%;
    font-size: 20px;
    font-weight: 600;
    border: none;
    border-radius: 3px;
    padding: 8px;
    background-color: white;
    box-shadow: inset 0 0 0 2px var(--primary-color);

    &:focus {
      outline: none;
    }
  }

  &__icon {
    position: absolute;
    left: 16px;
    top: 16px;
    color: var(--light-text);
  }

  &__header-actions {
    display: flex;
    align-items: center;
    position: absolute;
    top: 8px;
    right: 8px;
    z-index: 5;
  }

  &__menu-btn {
    color: var(--light-text);
    padding: 8px;
    border-radius: 50%;
    margin-right: 4px;

    &:hover {
      background-color: rgba(9, 30, 66, 0.08);
      color: var(--text-color);
    }
  }

  &__close {
    padding: 8px;
    border-radius: 50%;
    color: var(--light-text);

    &:hover {
      background-color: rgba(9, 30, 66, 0.08);
      color: var(--text-color);
    }
  }

  &__menu {
    position: absolute;
    top: 100%;
    right: 0;
    background-color: white;
    border-radius: 3px;
    box-shadow: 0 8px 16px -4px rgba(9, 30, 66, 0.25);
    width: 200px;
    z-index: 10;
    overflow: hidden;
  }

  &__submenu {
    background-color: #f4f5f7;
    border-top: 1px solid #dfe1e6;
    border-bottom: 1px solid #dfe1e6;
    padding: 4px 0;
  }

  &__menu-item {
    display: flex;
    align-items: center;
    width: 100%;
    text-align: left;
    padding: 8px 12px;
    color: var(--text-color);
    font-size: 14px;
    border-bottom: 1px solid var(--border-color);
    position: relative;

    svg {
      margin-right: 8px;
    }

    &:hover {
      background-color: rgba(9, 30, 66, 0.04);
    }

    &:last-child {
      border-bottom: none;
    }

    &--selected {
      background-color: rgba(9, 30, 66, 0.08);
      font-weight: 500;
    }

    &--delete {
      color: #eb5a46;

      &:hover {
        background-color: #fdf1f0;
      }
    }
  }

  .priority-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 4px;

    &--low {
      background-color: #1e7e34;
    }

    &--medium {
      background-color: #6c757d;
    }

    &--high {
      background-color: #856404;
    }

    &--top {
      background-color: #721c24;
    }
  }

  &__section {
    position: relative;
    padding: 0 40px 0 56px;
    margin-bottom: 24px;
  }

  &__color-section {
    position: relative;
    padding: 0 40px 0 56px;
    margin-bottom: 16px;
  }

  &__section-icon {
    position: absolute;
    left: 16px;
    top: 0;
    color: var(--light-text);
  }

  &__section-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 8px;
  }

  &__description {
    margin-bottom: 24px;
  }

  &__content-textarea {
    width: 100%;
    min-height: 108px;
    border: none;
    border-radius: 3px;
    padding: 8px 12px;
    background-color: white;
    box-shadow: inset 0 0 0 2px var(--primary-color);
    resize: vertical;
    font-family: inherit;
    font-size: 13px;
    line-height: 1.4;

    &:focus {
      outline: none;
    }
  }

  &__link-input {
    width: 100%;
    border: none;
    border-radius: 3px;
    padding: 8px 12px;
    background-color: white;
    box-shadow: inset 0 0 0 2px var(--primary-color);
    font-family: inherit;
    font-size: 13px;
    line-height: 1.4;

    &:focus {
      outline: none;
    }
  }

  &__link-display {
    min-height: 40px;
    padding: 8px 12px;
    background-color: rgba(9, 30, 66, 0.04);
    border-radius: 3px;
    cursor: pointer;
    font-size: 13px;
    line-height: 1.4;

    &:hover {
      background-color: rgba(9, 30, 66, 0.08);
    }
  }

  &__link {
    color: #0079bf;
    text-decoration: underline;
    word-break: break-all;
    font-size: 13px;

    &:hover {
      color: #026aa7;
    }
  }

  &__crypto-display {
    position: relative;
    width: 100%;
    min-height: 40px;
    padding: 8px 12px;
    background-color: rgba(9, 30, 66, 0.04);
    border-radius: 3px;
    font-size: 13px;
    line-height: 1.4;

    &:hover {
      background-color: rgba(9, 30, 66, 0.08);
    }

    &:hover .card-modal__comment-actions {
      opacity: 1;
    }
  }

  &__crypto-content {
    flex: 1;
    padding-right: 10px;
  }

  &__crypto-value {
    color: var(--text-color);
    word-break: break-all;
    font-size: 13px;
    font-family: monospace;
    display: block;
    padding: 4px 0;
  }

  &__content-display {
    min-height: 40px;
    padding: 8px 12px;
    background-color: rgba(9, 30, 66, 0.04);
    border-radius: 3px;
    cursor: pointer;
    font-size: 13px;
    line-height: 1.4;

    &:hover {
      background-color: rgba(9, 30, 66, 0.08);
    }
  }

  &__content-placeholder {
    color: var(--light-text);
    font-size: 13px;
  }

  &__rich-content {
    font-size: 12px;
    line-height: 1.4;
    word-break: break-word;

    p {
      margin: 0 0 6px 0;

      &:last-child {
        margin-bottom: 0;
      }
    }

    ul, ol {
      margin: 0 0 6px 0;
      padding-left: 20px;
    }

    a {
      color: var(--primary-color);
      text-decoration: none;

      &:hover {
        text-decoration: underline;
      }
    }

    strong, b {
      font-weight: 600;
    }

    img {
      max-width: 100%;
      height: auto;
      cursor: pointer;
      border-radius: 3px;
      margin: 4px 0;
      transition: opacity 0.2s ease;

      &:hover {
        opacity: 0.8;
      }
    }
  }

  &__content-actions {
    margin-top: 8px;
    display: flex;
    align-items: center;

    button {
      margin-right: 8px;
      padding: 6px 12px;
      border-radius: 3px;
      font-weight: 400;
      font-size: 14px;

      &:first-child {
        background-color: var(--primary-color);
        color: white;

        &:hover {
          background-color: #026aa7;
        }
      }

      &:last-child {
        color: var(--light-text);

        &:hover {
          background-color: rgba(9, 30, 66, 0.08);
          color: var(--text-color);
        }
      }
    }
  }

  &__comments {
    margin-top: 24px;
  }

  &__add-comment {
    margin-bottom: 16px;
  }

  &__comment-list {
    margin-top: 16px;
  }

  &__comment {
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 3px;
    padding: 8px 10px;
    margin-bottom: 12px;
    box-shadow: 0 1px 2px rgba(9, 30, 66, 0.2);
    position: relative;
    margin-left: 36px;

    &::before {
      content: '';
      position: absolute;
      left: -36px;
      top: 0;
      width: 28px;
      height: 28px;
      background-color: #dfe1e6;
      border-radius: 50%;
      background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23172b4d"><path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/></svg>');
      background-size: 65%;
      background-position: center;
      background-repeat: no-repeat;
    }
  }

  &__comment-header {
    display: flex;
    align-items: center;
    margin-bottom: 6px;

    strong {
      font-weight: 600;
      font-size: 13px;
      color: var(--text-color);
    }
  }

  &__comment-content {
    font-size: 12px;
    line-height: 1.4;
    margin-bottom: 6px;
    word-break: break-word;

    p {
      margin: 0 0 6px 0;

      &:last-child {
        margin-bottom: 0;
      }
    }

    ul, ol {
      margin: 0 0 6px 0;
      padding-left: 20px;
    }

    a {
      color: var(--primary-color);
      text-decoration: none;

      &:hover {
        text-decoration: underline;
      }
    }

    strong, b {
      font-weight: 600;
    }

    img {
      max-width: 100%;
      height: auto;
      cursor: pointer;
      border-radius: 3px;
      margin: 4px 0;
      transition: opacity 0.2s ease;

      &:hover {
        opacity: 0.8;
      }
    }
  }

  &__comment-date {
    font-size: 11px;
    color: var(--light-text);
    display: flex;
    align-items: center;
    margin-left: 6px;

    &::before {
      content: '•';
      margin: 0 4px;
    }
  }

  &__comment-actions {
    margin-top: 6px;
    display: flex;
    align-items: center;
    font-size: 11px;
    color: var(--light-text);
    opacity: 0;
    transition: opacity 0.2s ease;
  }

  // Special positioning for crypto field edit buttons
  &__crypto-display &__comment-actions {
    position: absolute;
    top: 50%;
    right: 12px;
    transform: translateY(-50%);
    margin-top: 0;
  }

  &__comment:hover &__comment-actions {
    opacity: 1;
  }

  &__comment-action-btn {
    color: var(--light-text);
    font-size: 11px;
    padding: 0;
    background: none;
    border: none;
    cursor: pointer;

    &:hover {
      color: var(--text-color);
      text-decoration: underline;
    }
  }

  &__comment-action-separator {
    margin: 0 3px;
  }



  &__actions {
    margin-top: 16px;
    border-top: 1px solid var(--border-color);
    padding-top: 16px;
  }

  &__delete-btn {
    background-color: #eb5a46;
    color: white;
    border-radius: 3px;
    padding: 6px 12px;
    font-weight: 400;
    font-size: 14px;

    &:hover {
      background-color: #cf513d;
    }
  }
}

.card-modal {
  &__priority-selector {
    margin-top: 8px;
  }

  &__priority-select {
    padding: 8px 12px;
    border-radius: 3px;
    border: 1px solid #dfe1e6;
    background-color: white;
    font-size: 14px;
    width: 150px;
  }
}

.color-picker {
  display: flex;
  flex-wrap: wrap;
  margin-top: 8px;

  &__option {
    width: 30px;
    height: 30px;
    border-radius: 4px;
    margin: 0 8px 8px 0;
    cursor: pointer;
    border: 2px solid transparent;
    transition: transform 0.1s ease;

    &:hover {
      transform: scale(1.1);
    }

    &--selected {
      border-color: var(--text-color);
      box-shadow: 0 0 0 2px white, 0 0 0 4px var(--border-color);
    }
  }
}

.checklist {
  margin-bottom: 24px;
  width: 100%;

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
  }

  &__title {
    font-size: 16px;
    font-weight: 600;
    display: flex;
    align-items: center;

    svg {
      margin-right: 8px;
    }
  }

  &__progress-wrapper {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
  }

  &__progress-text {
    font-size: 11px;
    color: var(--light-text);
    margin-right: 8px;
    min-width: 45px;
  }

  &__progress {
    height: 8px;
    background-color: #e0e0e0;
    border-radius: 4px;
    flex: 1;
    overflow: hidden;
  }

  &__progress-bar {
    height: 100%;
    background-color: #5ba4cf;
    transition: width 0.2s ease;
  }

  &__items {
    margin-bottom: 8px;
  }

  &__item {
    display: flex;
    align-items: center;
    padding: 6px 0;
    position: relative;

    &:hover {
      background-color: rgba(9, 30, 66, 0.04);
    }

    &-checkbox {
      appearance: none;
      -webkit-appearance: none;
      width: 16px;
      height: 16px;
      border: 2px solid #dfe1e6;
      border-radius: 2px;
      margin-right: 8px;
      position: relative;
      cursor: pointer;
      flex-shrink: 0;

      &:checked {
        background-color: #0079bf;
        border-color: #0079bf;
      }

      &:checked::after {
        content: '';
        position: absolute;
        left: 4px;
        top: 1px;
        width: 5px;
        height: 9px;
        border: solid white;
        border-width: 0 2px 2px 0;
        transform: rotate(45deg);
      }

      &:focus {
        outline: none;
        box-shadow: 0 0 0 2px rgba(0, 121, 191, 0.2);
      }
    }

    &-text {
      flex: 1;
      font-size: 14px;
      line-height: 20px;
      word-break: break-word;
      padding: 0 4px;

      &--completed {
        text-decoration: line-through;
        color: var(--light-text);
      }
    }

    &-delete {
      opacity: 0;
      color: var(--light-text);
      padding: 4px;
      border-radius: 3px;
      background: none;
      border: none;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;

      &:hover {
        background-color: rgba(9, 30, 66, 0.08);
        color: var(--text-color);
      }
    }

    &:hover &-delete {
      opacity: 1;
    }
  }

  &__add-item {
    display: flex;
    margin-top: 8px;
  }

  &__input {
    flex: 1;
    border: none;
    border-radius: 3px;
    padding: 8px 12px;
    background-color: white;
    box-shadow: 0 1px 0 rgba(9, 30, 66, 0.25);
    font-family: inherit;
    font-size: 14px;
    margin-right: 8px;

    &:focus {
      outline: none;
      box-shadow: inset 0 0 0 2px var(--primary-color);
    }
  }

  &__add-btn {
    background-color: var(--primary-color);
    color: white;
    padding: 6px 12px;
    border-radius: 3px;
    font-weight: 400;
    font-size: 14px;
    border: none;
    cursor: pointer;

    &:hover {
      background-color: #026aa7;
    }

    &:disabled {
      background-color: rgba(9, 30, 66, 0.04);
      color: var(--light-text);
      cursor: not-allowed;
    }
  }

  &__hide-completed {
    font-size: 14px;
    color: var(--light-text);
    background: none;
    border: none;
    padding: 4px 0;
    cursor: pointer;
    margin-top: 8px;
    display: flex;
    align-items: center;

    &:hover {
      color: var(--text-color);
    }

    svg {
      margin-right: 4px;
    }
  }
}

.comment-editor {
  margin-bottom: 12px;
  position: relative;

  &__upload-indicator {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    z-index: 10;
    border-radius: 3px;
    font-size: 12px;
    color: var(--text-color);
  }

  &__spinner {
    width: 16px;
    height: 16px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #0079bf;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  &__quill {
    .ql-toolbar {
      border-top-left-radius: 3px;
      border-top-right-radius: 3px;
      border-color: #dfe1e6;
      background-color: #f4f5f7;
      padding: 5px;
    }

    .ql-container {
      border-bottom-left-radius: 3px;
      border-bottom-right-radius: 3px;
      border-color: #dfe1e6;
      background-color: white;
      min-height: 70px;
      font-family: inherit;
      font-size: 12px;
    }

    .ql-editor {
      min-height: 70px;
      max-height: 180px;
      overflow-y: auto;
      padding: 8px;

      &.ql-blank::before {
        font-style: normal;
        color: var(--light-text);
      }

      img {
        max-width: 100%;
        height: auto;
        cursor: pointer;
        border-radius: 3px;
        transition: opacity 0.2s ease;

        &:hover {
          opacity: 0.8;
        }
      }
    }
  }

  &__actions {
    display: flex;
    margin-top: 6px;
  }

  &__save-btn {
    background-color: var(--primary-color);
    color: white;
    padding: 4px 10px;
    border-radius: 3px;
    font-weight: 400;
    font-size: 12px;
    margin-right: 6px;

    &:hover {
      background-color: #026aa7;
    }

    &:disabled {
      background-color: rgba(9, 30, 66, 0.04);
      color: var(--light-text);
      cursor: not-allowed;
    }
  }

  &__cancel-btn {
    color: var(--light-text);
    padding: 4px 10px;
    border-radius: 3px;
    font-weight: 400;
    font-size: 12px;

    &:hover {
      background-color: rgba(9, 30, 66, 0.08);
      color: var(--text-color);
    }
  }
}

.comment-editor-placeholder {
  width: 100%;
  height: 100px;
  background-color: #f4f5f7;
  border-radius: 3px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 6px;

  .comment-editor-loading {
    color: var(--light-text);
    font-size: 12px;
  }
}

.search-box {
  position: relative;
  width: 240px;
  height: 32px;

  &__icon {
    position: absolute;
    left: 8px;
    top: 50%;
    transform: translateY(-50%);
    color: rgba(255, 255, 255, 0.7);
  }

  &__input {
    width: 100%;
    height: 100%;
    padding: 0 32px 0 32px;
    border-radius: 3px;
    border: none;
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
    font-size: 14px;

    &::placeholder {
      color: rgba(255, 255, 255, 0.7);
    }

    &:focus {
      outline: none;
      background-color: rgba(255, 255, 255, 0.3);
    }
  }

  &__clear {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    color: rgba(255, 255, 255, 0.7);
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      color: white;
    }
  }
}

// Keyframes
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// Image overlay styles
.image-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  cursor: pointer;

  &__content {
    position: relative;
    max-width: 90vw;
    max-height: 90vh;
    cursor: default;
  }

  &__close {
    position: absolute;
    top: -40px;
    right: 0;
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    padding: 8px;
    border-radius: 3px;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: rgba(255, 255, 255, 0.1);
    }

    svg {
      width: 24px;
      height: 24px;
    }
  }

  &__loading {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 200px;
    height: 200px;
    color: white;
  }

  &__spinner {
    width: 32px;
    height: 32px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-top: 3px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  &__image {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    border-radius: 3px;
  }
}

// Login required and no boards states
.login-required,
.no-boards {
  height: calc(100vh - var(--cryptodo-header-height));
  display: flex;
  align-items: center;
  justify-content: center;
  background-image: url('https://images.unsplash.com/photo-1575516478880-7dfb1a114073?q=80&w=2072&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D');
  background-size: cover;
  background-position: center;

  &__content {
    text-align: center;
    background: rgba(255, 255, 255, 0.95);
    padding: 48px;
    border-radius: 12px;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    max-width: 500px;
    margin: 0 20px;

    h1 {
      font-size: 32px;
      font-weight: 600;
      color: #111827;
      margin-bottom: 16px;
    }

    p {
      font-size: 18px;
      color: #6b7280;
      margin-bottom: 32px;
      line-height: 1.6;
    }
  }

  &__icon {
    color: #6b7280;
    opacity: 0.7;
  }
}

// Import authentication styles
@import './auth.scss';