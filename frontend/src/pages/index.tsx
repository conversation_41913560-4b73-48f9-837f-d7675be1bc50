import { useState, useEffect } from 'react';
import Head from 'next/head';
import Board from '@/components/Board';
import Header from '@/components/Header';
import { gql, useQuery } from '@apollo/client';
import { useAuth } from '@/contexts/AuthContext';

// GraphQL queries and mutations
const GET_USER_BOARD = gql`
  query GetUserBoard($boardId: ID) {
    userBoard(boardId: $boardId) {
      _id
      name
      swimlanes {
        _id
        title
        cards {
          _id
          title
          content
          color
          link
          evmAddress
          privateKey
          createdAt
          updatedAt
          priority
          comments {
            _id
            content
            createdAt
          }
          checklistItems {
            _id
            text
            completed
            position
          }
        }
      }
    }
  }
`;

export default function Home() {
  const { user, loading: authLoading } = useAuth();
  const [currentBoardId, setCurrentBoardId] = useState<string | null>(null);

  // Only query user boards when authenticated
  const { loading: boardLoading, error, data, refetch } = useQuery(
    GET_USER_BOARD,
    {
      variables: { boardId: currentBoardId },
      skip: authLoading || !user, // Skip if not authenticated
    }
  );

  const handleBoardChange = async (boardId: string) => {
    setCurrentBoardId(boardId);
    await refetch({ boardId });
  };

  if (authLoading) return <div>Loading...</div>;

  // Show login prompt if not authenticated
  if (!user) {
    return (
      <>
        <Head>
          <title>Cryptodo</title>
          <meta name="description" content="A cryptodo application" />
          <meta name="viewport" content="width=device-width, initial-scale=1" />
          <link rel="icon" href="/favicon.ico" />
        </Head>
        <main>
          <Header
            searchQuery=""
            setSearchQuery={() => {}}
            currentBoardId={undefined}
            onBoardChange={undefined}
          />
          <div className="login-required">
            <div className="login-required__content">
              <h1>Welcome to Cryptodo</h1>
              <p>Please sign in to access your boards and start organizing your tasks.</p>
              <div className="login-required__icon">
                <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <rect x="3" y="11" width="18" height="11" rx="2" ry="2" />
                  <circle cx="12" cy="16" r="1" />
                  <path d="M7 11V7a5 5 0 0 1 10 0v4" />
                </svg>
              </div>
            </div>
          </div>
        </main>
      </>
    );
  }

  if (boardLoading) return <div>Loading...</div>;
  if (error) return <div>Error loading board: {error.message}</div>;

  const boardData = data?.userBoard;

  if (!boardData) {
    return (
      <>
        <Head>
          <title>Cryptodo</title>
          <meta name="description" content="A cryptodo application" />
          <meta name="viewport" content="width=device-width, initial-scale=1" />
          <link rel="icon" href="/favicon.ico" />
        </Head>
        <main>
          <Header
            searchQuery=""
            setSearchQuery={() => {}}
            currentBoardId={undefined}
            onBoardChange={handleBoardChange}
          />
          <div className="no-boards">
            <div className="no-boards__content">
              <h1>No Boards Found</h1>
              <p>Create your first board to get started!</p>
            </div>
          </div>
        </main>
      </>
    );
  }

  return (
    <>
      <Head>
        <title>Cryptodo</title>
        <meta name="description" content="A cryptodo application" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
      </Head>
      <main>
        <Board board={boardData} onBoardChange={handleBoardChange} />
      </main>
    </>
  );
}
