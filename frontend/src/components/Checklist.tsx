import { useState, useEffect } from 'react';
import { ChecklistItem as ChecklistItemType } from '@/types';
import { gql, useMutation } from '@apollo/client';

const ADD_CHECKLIST_ITEM = gql`
  mutation AddChecklistItem($createChecklistItemInput: CreateChecklistItemInput!) {
    addChecklistItem(createChecklistItemInput: $createChecklistItemInput) {
      _id
      text
      completed
      position
    }
  }
`;

const TOGGLE_CHECKLIST_ITEM = gql`
  mutation ToggleChecklistItem($id: ID!) {
    toggleChecklistItem(id: $id) {
      _id
      completed
    }
  }
`;

const REMOVE_CHECKLIST_ITEM = gql`
  mutation RemoveChecklistItem($id: ID!) {
    removeChecklistItem(id: $id) {
      _id
    }
  }
`;

interface ChecklistProps {
  cardId: string;
  items: ChecklistItemType[];
}

const Checklist = ({ cardId, items }: ChecklistProps) => {
  const [newItemText, setNewItemText] = useState('');
  const [hideCompleted, setHideCompleted] = useState(false);
  const [localItems, setLocalItems] = useState<ChecklistItemType[]>(items);
  const [addChecklistItem] = useMutation(ADD_CHECKLIST_ITEM);
  const [toggleChecklistItem] = useMutation(TOGGLE_CHECKLIST_ITEM);
  const [removeChecklistItem] = useMutation(REMOVE_CHECKLIST_ITEM);

  // Update local items when props change
  useEffect(() => {
    setLocalItems(items);
  }, [items]);

  const completedCount = localItems.filter(item => item.completed).length;
  const progress = localItems.length > 0 ? (completedCount / localItems.length) * 100 : 0;
  const progressText = `${completedCount}/${localItems.length}`;

  const displayedItems = hideCompleted
    ? localItems.filter(item => !item.completed)
    : localItems;

  const handleAddItem = () => {
    if (newItemText.trim() === '') return;

    addChecklistItem({
      variables: {
        createChecklistItemInput: {
          text: newItemText,
          cardId,
        },
      },
      update: (cache, { data: { addChecklistItem } }) => {
        cache.modify({
          id: cache.identify({ __typename: 'Card', _id: cardId }),
          fields: {
            checklistItems(existingItems = []) {
              const newItemRef = cache.writeFragment({
                data: addChecklistItem,
                fragment: gql`
                  fragment NewChecklistItem on ChecklistItem {
                    _id
                    text
                    completed
                    position
                  }
                `,
              });
              return [...existingItems, newItemRef];
            },
          },
        });

        // Update local state immediately
        setLocalItems(prevItems => [...prevItems, addChecklistItem]);
      },
    });

    setNewItemText('');
  };

  const handleToggleItem = (id: string) => {
    const item = localItems.find(item => item._id === id);
    if (!item) return;

    const newCompletedValue = !item.completed;

    toggleChecklistItem({
      variables: { id },
      optimisticResponse: {
        toggleChecklistItem: {
          __typename: 'ChecklistItem',
          _id: id,
          completed: newCompletedValue,
        },
      },
      update: (cache) => {
        // Update local state immediately
        setLocalItems(prevItems =>
          prevItems.map(item =>
            item._id === id ? { ...item, completed: newCompletedValue } : item
          )
        );
      },
    });
  };

  const handleRemoveItem = (id: string) => {
    removeChecklistItem({
      variables: { id },
      update: (cache) => {
        cache.modify({
          id: cache.identify({ __typename: 'Card', _id: cardId }),
          fields: {
            checklistItems(existingItems = [], { readField }) {
              return existingItems.filter(
                (itemRef: any) => readField('_id', itemRef) !== id
              );
            },
          },
        });

        // Update local state immediately
        setLocalItems(prevItems => prevItems.filter(item => item._id !== id));
      },
    });
  };

  return (
    <div className="checklist">
      <div className="checklist__header">
        <h3 className="checklist__title">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <path d="M9 11l3 3L22 4" />
            <path d="M21 12v7a2 2 0 01-2 2H5a2 2 0 01-2-2V5a2 2 0 012-2h11" />
          </svg>
          Checklist
        </h3>
      </div>

      <div className="checklist__progress-wrapper">
        <span className="checklist__progress-text">{progressText}</span>
        <div className="checklist__progress">
          <div
            className="checklist__progress-bar"
            style={{ width: `${progress}%` }}
          />
        </div>
      </div>

      <div className="checklist__items">
        {displayedItems.map(item => (
          <div key={item._id} className="checklist__item">
            <input
              type="checkbox"
              checked={item.completed}
              onChange={() => handleToggleItem(item._id)}
              className="checklist__item-checkbox"
            />
            <div className={`checklist__item-text ${item.completed ? 'checklist__item-text--completed' : ''}`}>
              {item.text}
            </div>
            <button
              className="checklist__item-delete"
              onClick={() => handleRemoveItem(item._id)}
            >
              <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="M18 6L6 18M6 6l12 12" />
              </svg>
            </button>
          </div>
        ))}
      </div>

      <div className="checklist__add-item">
        <input
          type="text"
          placeholder="Add an item"
          value={newItemText}
          onChange={(e) => setNewItemText(e.target.value)}
          className="checklist__input"
          onKeyDown={(e) => e.key === 'Enter' && handleAddItem()}
        />
        <button
          className="checklist__add-btn"
          onClick={handleAddItem}
          disabled={newItemText.trim() === ''}
        >
          Add
        </button>
      </div>

      {completedCount > 0 && (
        <button
          className="checklist__hide-completed"
          onClick={() => setHideCompleted(!hideCompleted)}
        >
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            {hideCompleted ? (
              <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z" />
            ) : (
              <path d="M17.94 17.94A10.07 10.07 0 0112 20c-7 0-11-8-11-8a18.45 18.45 0 015.06-5.94M9.9 4.24A9.12 9.12 0 0112 4c7 0 11 8 11 8a18.5 18.5 0 01-2.16 3.19m-6.72-1.07a3 3 0 11-4.24-4.24M1 1l22 22" />
            )}
          </svg>
          {hideCompleted ? 'Show completed items' : 'Hide completed items'}
        </button>
      )}
    </div>
  );
};

export default Checklist;
