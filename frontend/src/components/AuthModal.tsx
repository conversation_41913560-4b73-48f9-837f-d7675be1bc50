import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { GoogleOAuthProvider, GoogleLogin } from '@react-oauth/google';
import { useAuth } from '@/contexts/AuthContext';

interface AuthModalProps {
  isOpen: boolean;
  onClose: () => void;
}

interface LoginFormData {
  email: string;
  password: string;
}

interface RegisterFormData {
  email: string;
  password: string;
  confirmPassword: string;
  name: string;
}

interface PasswordResetFormData {
  email: string;
}

const AuthModal: React.FC<AuthModalProps> = ({ isOpen, onClose }) => {
  const [mode, setMode] = useState<'login' | 'register' | 'reset'>('login');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [message, setMessage] = useState<string | null>(null);

  const { login, register, oauthLogin, requestPasswordReset } = useAuth();

  const {
    register: registerField,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
  } = useForm<any>();

  const password = watch('password');

  const handleLogin = async (data: LoginFormData) => {
    setLoading(true);
    setError(null);
    try {
      await login(data.email, data.password);
      onClose();
      reset();
    } catch (err: any) {
      setError(err.message || 'Login failed');
    } finally {
      setLoading(false);
    }
  };

  const handleRegister = async (data: RegisterFormData) => {
    setLoading(true);
    setError(null);
    try {
      await register(data.email, data.password, data.name);
      onClose();
      reset();
    } catch (err: any) {
      setError(err.message || 'Registration failed');
    } finally {
      setLoading(false);
    }
  };

  const handlePasswordReset = async (data: PasswordResetFormData) => {
    setLoading(true);
    setError(null);
    try {
      await requestPasswordReset(data.email);
      setMessage('Password reset email sent. Please check your inbox.');
    } catch (err: any) {
      setError(err.message || 'Password reset failed');
    } finally {
      setLoading(false);
    }
  };

  const handleGoogleSuccess = async (credentialResponse: any) => {
    try {
      if (!credentialResponse.credential) {
        throw new Error('No credential received from Google');
      }

      // Decode the JWT token to get user info
      const base64Url = credentialResponse.credential.split('.')[1];
      const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
      const jsonPayload = decodeURIComponent(
        atob(base64)
          .split('')
          .map((c) => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
          .join('')
      );
      const decoded = JSON.parse(jsonPayload);

      await oauthLogin(
        'google',
        decoded.sub,
        decoded.email,
        decoded.name || decoded.given_name + ' ' + decoded.family_name,
        decoded.picture
      );
      onClose();
    } catch (err: any) {
      console.error('Google login error:', err);
      setError('Google login failed: ' + (err.message || 'Unknown error'));
    }
  };

  const handleGoogleError = () => {
    setError('Google login failed');
  };

  const switchMode = (newMode: 'login' | 'register' | 'reset') => {
    setMode(newMode);
    setError(null);
    setMessage(null);
    reset();
  };

  if (!isOpen) return null;

  return (
    <div className="auth-modal-overlay" onClick={onClose}>
      <div className="auth-modal" onClick={(e) => e.stopPropagation()}>
        <div className="auth-modal__header">
          <h2>
            {mode === 'login' && 'Sign In'}
            {mode === 'register' && 'Sign Up'}
            {mode === 'reset' && 'Reset Password'}
          </h2>
          <button className="auth-modal__close" onClick={onClose}>
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path d="M18 6L6 18M6 6l12 12" />
            </svg>
          </button>
        </div>

        <div className="auth-modal__content">
          {error && <div className="auth-modal__error">{error}</div>}
          {message && <div className="auth-modal__message">{message}</div>}

          {mode === 'login' && (
            <form onSubmit={handleSubmit(handleLogin)} className="auth-form">
              <div className="auth-form__field">
                <label htmlFor="email">Email</label>
                <input
                  type="email"
                  id="email"
                  {...registerField('email', { required: 'Email is required' })}
                />
                {errors.email && <span className="auth-form__error">{errors.email.message as string}</span>}
              </div>

              <div className="auth-form__field">
                <label htmlFor="password">Password</label>
                <input
                  type="password"
                  id="password"
                  {...registerField('password', { required: 'Password is required' })}
                />
                {errors.password && <span className="auth-form__error">{errors.password.message as string}</span>}
              </div>

              <button type="submit" className="auth-form__submit" disabled={loading}>
                {loading ? 'Signing In...' : 'Sign In'}
              </button>
            </form>
          )}

          {mode === 'register' && (
            <form onSubmit={handleSubmit(handleRegister)} className="auth-form">
              <div className="auth-form__field">
                <label htmlFor="name">Name</label>
                <input
                  type="text"
                  id="name"
                  {...registerField('name', { required: 'Name is required' })}
                />
                {errors.name && <span className="auth-form__error">{errors.name.message as string}</span>}
              </div>

              <div className="auth-form__field">
                <label htmlFor="email">Email</label>
                <input
                  type="email"
                  id="email"
                  {...registerField('email', { required: 'Email is required' })}
                />
                {errors.email && <span className="auth-form__error">{errors.email.message as string}</span>}
              </div>

              <div className="auth-form__field">
                <label htmlFor="password">Password</label>
                <input
                  type="password"
                  id="password"
                  {...registerField('password', {
                    required: 'Password is required',
                    minLength: { value: 6, message: 'Password must be at least 6 characters' }
                  })}
                />
                {errors.password && <span className="auth-form__error">{errors.password.message as string}</span>}
              </div>

              <div className="auth-form__field">
                <label htmlFor="confirmPassword">Confirm Password</label>
                <input
                  type="password"
                  id="confirmPassword"
                  {...registerField('confirmPassword', {
                    required: 'Please confirm your password',
                    validate: (value) => value === password || 'Passwords do not match'
                  })}
                />
                {errors.confirmPassword && <span className="auth-form__error">{errors.confirmPassword.message as string}</span>}
              </div>

              <button type="submit" className="auth-form__submit" disabled={loading}>
                {loading ? 'Creating Account...' : 'Sign Up'}
              </button>
            </form>
          )}

          {mode === 'reset' && (
            <form onSubmit={handleSubmit(handlePasswordReset)} className="auth-form">
              <div className="auth-form__field">
                <label htmlFor="email">Email</label>
                <input
                  type="email"
                  id="email"
                  {...registerField('email', { required: 'Email is required' })}
                />
                {errors.email && <span className="auth-form__error">{errors.email.message as string}</span>}
              </div>

              <button type="submit" className="auth-form__submit" disabled={loading}>
                {loading ? 'Sending...' : 'Send Reset Email'}
              </button>
            </form>
          )}

          {(mode === 'login' || mode === 'register') && (
            <div className="auth-modal__oauth">
              <div className="auth-modal__divider">
                <span>or</span>
              </div>

              {process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID && process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID !== 'your-actual-google-client-id-here' ? (
                <GoogleOAuthProvider clientId={process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID}>
                  <GoogleLogin
                    onSuccess={handleGoogleSuccess}
                    onError={handleGoogleError}
                    theme="outline"
                    size="large"
                    width="100%"
                  />
                </GoogleOAuthProvider>
              ) : (
                <div className="auth-modal__google-disabled">
                  <p>Google Sign-In is not configured.</p>
                  <p>Please set up Google OAuth in the environment variables.</p>
                </div>
              )}
            </div>
          )}

          <div className="auth-modal__footer">
            {mode === 'login' && (
              <>
                <p>
                  Don't have an account?{' '}
                  <button type="button" onClick={() => switchMode('register')} className="auth-modal__link">
                    Sign up
                  </button>
                </p>
                <p>
                  <button type="button" onClick={() => switchMode('reset')} className="auth-modal__link">
                    Forgot password?
                  </button>
                </p>
              </>
            )}
            {mode === 'register' && (
              <p>
                Already have an account?{' '}
                <button type="button" onClick={() => switchMode('login')} className="auth-modal__link">
                  Sign in
                </button>
              </p>
            )}
            {mode === 'reset' && (
              <p>
                Remember your password?{' '}
                <button type="button" onClick={() => switchMode('login')} className="auth-modal__link">
                  Sign in
                </button>
              </p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AuthModal;
