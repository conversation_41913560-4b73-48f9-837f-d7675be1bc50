import { useState, useEffect } from 'react';

interface ImageOverlayProps {
  imageUrl: string;
  onClose: () => void;
}

const ImageOverlay = ({ imageUrl, onClose }: ImageOverlayProps) => {
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [onClose]);

  const handleImageLoad = () => {
    setIsLoading(false);
  };

  const handleOverlayClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <div className="image-overlay" onClick={handleOverlayClick}>
      <div className="image-overlay__content">
        <button className="image-overlay__close" onClick={onClose}>
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <path d="M18 6L6 18M6 6l12 12" />
          </svg>
        </button>
        {isLoading && (
          <div className="image-overlay__loading">
            <div className="image-overlay__spinner"></div>
          </div>
        )}
        <img
          src={imageUrl}
          alt="Full size"
          className="image-overlay__image"
          onLoad={handleImageLoad}
          style={{ display: isLoading ? 'none' : 'block' }}
        />
      </div>
    </div>
  );
};

export default ImageOverlay;
