import { useState } from 'react';
import Swimlane from './Swimlane';
import CardModal from './CardModal';
import { Card as CardType, Swimlane as SwimlaneType } from '@/types';
import Header from './Header';
import { DragDropContext, DropResult, Droppable, Draggable } from 'react-beautiful-dnd';
import { gql, useMutation, useApolloClient } from '@apollo/client';

interface BoardProps {
  board: {
    _id: string;
    name: string;
    swimlanes: SwimlaneType[];
  };
  onBoardChange?: (boardId: string) => void;
}

const MOVE_CARD = gql`
  mutation MoveCard($cardId: ID!, $sourceSwimlaneId: ID!, $destinationSwimlaneId: ID!, $position: Float!) {
    moveCard(
      cardId: $cardId,
      sourceSwimlaneId: $sourceSwimlaneId,
      destinationSwimlaneId: $destinationSwimlaneId,
      position: $position
    ) {
      _id
    }
  }
`;

const MOVE_SWIMLANE = gql`
  mutation MoveSwimlane($swimlaneId: ID!, $position: Float!) {
    moveSwimlane(
      swimlaneId: $swimlaneId,
      position: $position
    ) {
      _id
      position
    }
  }
`;

const CREATE_SWIMLANE = gql`
  mutation CreateSwimlane($createSwimlaneInput: CreateSwimlaneInput!) {
    createSwimlane(createSwimlaneInput: $createSwimlaneInput) {
      _id
      title
      boardId
    }
  }
`;

const Board = ({ board, onBoardChange }: BoardProps) => {
  const [selectedCard, setSelectedCard] = useState<CardType | null>(null);
  const [selectedSwimlaneId, setSelectedSwimlaneId] = useState<string | null>(null);
  const [isAddingList, setIsAddingList] = useState(false);
  const [newListTitle, setNewListTitle] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [moveCardMutation] = useMutation(MOVE_CARD);
  const [moveSwimlane] = useMutation(MOVE_SWIMLANE);
  const [createSwimlane] = useMutation(CREATE_SWIMLANE);
  const client = useApolloClient();

  const handleCardClick = (card: CardType, swimlaneId: string) => {
    setSelectedCard(card);
    setSelectedSwimlaneId(swimlaneId);
  };

  const handleCloseModal = () => {
    setSelectedCard(null);
    setSelectedSwimlaneId(null);
  };

  const handleDragEnd = (result: DropResult) => {
    const { destination, source, draggableId, type } = result;

    // Dropped outside the list
    if (!destination) {
      return;
    }

    // Dropped in the same position
    if (
      destination.droppableId === source.droppableId &&
      destination.index === source.index
    ) {
      return;
    }

    // Handle swimlane reordering
    if (type === 'SWIMLANE') {
      const swimlaneId = draggableId;

      // Move the swimlane in the backend
      moveSwimlane({
        variables: {
          swimlaneId,
          position: destination.index,
        },
        optimisticResponse: {
          moveSwimlane: {
            __typename: 'Swimlane',
            _id: swimlaneId,
            position: destination.index,
          },
        },
        update: (cache) => {
          // We'll let the query refetch to get the updated order
          cache.evict({ fieldName: 'board' });
        },
      });

      return;
    }

    // Handle card reordering
    // Find the card that was moved
    const sourceSwimlane = board.swimlanes.find(s => String(s._id) === source.droppableId);
    if (!sourceSwimlane) {
      console.error('Source swimlane not found:', source.droppableId);
      return;
    }

    const card = sourceSwimlane.cards[source.index];
    if (!card) {
      console.error('Card not found at index:', source.index);
      return;
    }

    const cardId = card._id;

    // Move the card in the backend
    moveCardMutation({
      variables: {
        cardId: String(cardId),
        sourceSwimlaneId: String(source.droppableId),
        destinationSwimlaneId: String(destination.droppableId),
        position: destination.index,
      },
      optimisticResponse: {
        moveCard: {
          __typename: 'Card',
          _id: String(cardId),
        },
      },
      update: (cache) => {
        // We'll let the query refetch to get the updated order
        cache.evict({ fieldName: 'board' });
      },
    });
  };

  const handleDragStart = (start: any) => {
  };

  const handleAddList = () => {
    setIsAddingList(true);
  };

  const handleCancelAddList = () => {
    setIsAddingList(false);
    setNewListTitle('');
  };

  const handleCreateList = () => {
    if (newListTitle.trim() === '') return;

    createSwimlane({
      variables: {
        createSwimlaneInput: {
          title: newListTitle,
          boardId: board._id,
        },
      },
      update: (cache, { data: { createSwimlane } }) => {
        // Update the board's swimlanes array in the cache
        cache.modify({
          id: cache.identify({ __typename: 'Board', _id: board._id }),
          fields: {
            swimlanes(existingSwimlanes = []) {
              const newSwimlaneRef = cache.writeFragment({
                data: createSwimlane,
                fragment: gql`
                  fragment NewSwimlane on Swimlane {
                    _id
                    title
                    boardId
                    cards {
                      _id
                    }
                  }
                `,
              });
              return [...existingSwimlanes, { ...newSwimlaneRef, cards: [] }];
            },
          },
        });
      },
    });

    setNewListTitle('');
    setIsAddingList(false);
  };

  return (
    <>
      <Header
        searchQuery={searchQuery}
        setSearchQuery={setSearchQuery}
        currentBoardId={board._id}
        onBoardChange={onBoardChange}
      />
      <DragDropContext
        onDragStart={handleDragStart}
        onDragEnd={handleDragEnd}
      >
        <Droppable droppableId="board" direction="horizontal" type="SWIMLANE">
          {(provided) => (
            <div
              className="board"
              ref={provided.innerRef}
              {...provided.droppableProps}
            >
              {board.swimlanes.map((swimlane, index) => (
                <Draggable
                  key={swimlane._id}
                  draggableId={String(swimlane._id)}
                  index={index}
                >
                  {(provided, snapshot) => (
                    <div
                      ref={provided.innerRef}
                      {...provided.draggableProps}
                      className={`swimlane-wrapper ${snapshot.isDragging ? 'is-dragging' : ''}`}
                    >
                      <Swimlane
                        swimlane={swimlane}
                        onCardClick={handleCardClick}
                        dragHandleProps={provided.dragHandleProps}
                        searchQuery={searchQuery}
                        onDelete={(swimlaneId) => {
                          // Update the board's swimlanes array in the cache
                          client.cache.modify({
                            id: client.cache.identify({ __typename: 'Board', _id: board._id }),
                            fields: {
                              swimlanes(existingSwimlanes = [], { readField }) {
                                return existingSwimlanes.filter(
                                  (swimlaneRef: any) => readField('_id', swimlaneRef) !== swimlaneId
                                );
                              },
                            },
                          });
                        }}
                      />
                    </div>
                  )}
                </Draggable>
              ))}
              {provided.placeholder}

              {isAddingList ? (
                <div className="add-list-form">
                  <input
                    type="text"
                    placeholder="Enter list title..."
                    value={newListTitle}
                    onChange={(e) => setNewListTitle(e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' && newListTitle.trim() !== '') {
                        handleCreateList();
                      } else if (e.key === 'Escape') {
                        handleCancelAddList();
                      }
                    }}
                    className="add-list-input"
                    autoFocus
                  />
                  <div className="add-list-actions">
                    <button
                      onClick={handleCreateList}
                      disabled={newListTitle.trim() === ''}
                      className="add-list-save-btn"
                    >
                      Add List
                    </button>
                    <button
                      onClick={handleCancelAddList}
                      className="add-list-cancel-btn"
                    >
                      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <line x1="18" y1="6" x2="6" y2="18" />
                        <line x1="6" y1="6" x2="18" y2="18" />
                      </svg>
                    </button>
                  </div>
                </div>
              ) : (
                <button className="add-list-btn" onClick={handleAddList}>
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12 4V20M4 12H20" stroke="currentColor" strokeWidth="2" />
                  </svg>
                  Add another list
                </button>
              )}
            </div>
          )}
        </Droppable>

        {selectedCard && selectedSwimlaneId && (
          <CardModal
            card={selectedCard}
            swimlaneId={selectedSwimlaneId}
            onClose={handleCloseModal}
          />
        )}
      </DragDropContext>
    </>
  );
};

export default Board;
