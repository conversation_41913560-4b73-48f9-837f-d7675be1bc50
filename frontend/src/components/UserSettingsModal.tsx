import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { useAuth } from '@/contexts/AuthContext';

interface UserSettingsModalProps {
  isOpen: boolean;
  onClose: () => void;
}

interface ProfileFormData {
  name: string;
  email: string;
  language: string;
}

interface PasswordFormData {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

const UserSettingsModal: React.FC<UserSettingsModalProps> = ({ isOpen, onClose }) => {
  const [activeTab, setActiveTab] = useState<'profile' | 'password'>('profile');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [message, setMessage] = useState<string | null>(null);

  const { user, updateProfile, changePassword } = useAuth();

  const {
    register: registerProfileField,
    handleSubmit: handleProfileSubmit,
    formState: { errors: profileErrors },
    reset: resetProfile,
  } = useForm<ProfileFormData>();

  const {
    register: registerPasswordField,
    handleSubmit: handlePasswordSubmit,
    formState: { errors: passwordErrors },
    reset: resetPassword,
    watch,
  } = useForm<PasswordFormData>();

  const newPassword = watch('newPassword');

  useEffect(() => {
    if (user) {
      resetProfile({
        name: user.name,
        email: user.email,
        language: user.language || 'en',
      });
    }
  }, [user, resetProfile]);

  const handleProfileUpdate = async (data: ProfileFormData) => {
    setLoading(true);
    setError(null);
    setMessage(null);
    try {
      await updateProfile(data);
      setMessage('Profile updated successfully');
    } catch (err: any) {
      setError(err.message || 'Failed to update profile');
    } finally {
      setLoading(false);
    }
  };

  const handlePasswordChange = async (data: PasswordFormData) => {
    setLoading(true);
    setError(null);
    setMessage(null);
    try {
      await changePassword(data.currentPassword, data.newPassword);
      setMessage('Password changed successfully');
      resetPassword();
    } catch (err: any) {
      setError(err.message || 'Failed to change password');
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="settings-modal-overlay" onClick={onClose}>
      <div className="settings-modal" onClick={(e) => e.stopPropagation()}>
        <div className="settings-modal__header">
          <h2>User Settings</h2>
          <button className="settings-modal__close" onClick={onClose}>
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path d="M18 6L6 18M6 6l12 12" />
            </svg>
          </button>
        </div>

        <div className="settings-modal__tabs">
          <button
            className={`settings-modal__tab ${activeTab === 'profile' ? 'active' : ''}`}
            onClick={() => setActiveTab('profile')}
          >
            Profile
          </button>
          <button
            className={`settings-modal__tab ${activeTab === 'password' ? 'active' : ''}`}
            onClick={() => setActiveTab('password')}
          >
            Password
          </button>
        </div>

        <div className="settings-modal__content">
          {error && <div className="settings-modal__error">{error}</div>}
          {message && <div className="settings-modal__message">{message}</div>}

          {activeTab === 'profile' && (
            <form onSubmit={handleProfileSubmit(handleProfileUpdate)} className="settings-form">
              <div className="settings-form__field">
                <label htmlFor="name">Name</label>
                <input
                  type="text"
                  id="name"
                  {...registerProfileField('name', { required: 'Name is required' })}
                />
                {profileErrors.name && (
                  <span className="settings-form__error">{profileErrors.name.message}</span>
                )}
              </div>

              <div className="settings-form__field">
                <label htmlFor="email">Email</label>
                <input
                  type="email"
                  id="email"
                  {...registerProfileField('email', { required: 'Email is required' })}
                />
                {profileErrors.email && (
                  <span className="settings-form__error">{profileErrors.email.message}</span>
                )}
              </div>

              <div className="settings-form__field">
                <label htmlFor="language">Language</label>
                <select
                  id="language"
                  {...registerProfileField('language')}
                >
                  <option value="en">English</option>
                  <option value="de">Deutsch</option>
                  <option value="fr">Français</option>
                  <option value="es">Español</option>
                  <option value="it">Italiano</option>
                </select>
              </div>

              <div className="settings-form__oauth-info">
                <h4>Connected Accounts</h4>
                <div className="oauth-providers">
                  {user?.oauthProviders?.includes('google') && (
                    <div className="oauth-provider">
                      <span className="oauth-provider__icon">🔗</span>
                      <span>Google</span>
                    </div>
                  )}
                  {user?.oauthProviders?.includes('apple') && (
                    <div className="oauth-provider">
                      <span className="oauth-provider__icon">🔗</span>
                      <span>Apple</span>
                    </div>
                  )}
                  {(!user?.oauthProviders || user.oauthProviders.length === 0) && (
                    <p className="no-oauth">No connected accounts</p>
                  )}
                </div>
              </div>

              <button type="submit" className="settings-form__submit" disabled={loading}>
                {loading ? 'Updating...' : 'Update Profile'}
              </button>
            </form>
          )}

          {activeTab === 'password' && (
            <form onSubmit={handlePasswordSubmit(handlePasswordChange)} className="settings-form">
              <div className="settings-form__field">
                <label htmlFor="currentPassword">Current Password</label>
                <input
                  type="password"
                  id="currentPassword"
                  {...registerPasswordField('currentPassword', { required: 'Current password is required' })}
                />
                {passwordErrors.currentPassword && (
                  <span className="settings-form__error">{passwordErrors.currentPassword.message}</span>
                )}
              </div>

              <div className="settings-form__field">
                <label htmlFor="newPassword">New Password</label>
                <input
                  type="password"
                  id="newPassword"
                  {...registerPasswordField('newPassword', {
                    required: 'New password is required',
                    minLength: { value: 6, message: 'Password must be at least 6 characters' },
                  })}
                />
                {passwordErrors.newPassword && (
                  <span className="settings-form__error">{passwordErrors.newPassword.message}</span>
                )}
              </div>

              <div className="settings-form__field">
                <label htmlFor="confirmPassword">Confirm New Password</label>
                <input
                  type="password"
                  id="confirmPassword"
                  {...registerPasswordField('confirmPassword', {
                    required: 'Please confirm your new password',
                    validate: (value) => value === newPassword || 'Passwords do not match',
                  })}
                />
                {passwordErrors.confirmPassword && (
                  <span className="settings-form__error">{passwordErrors.confirmPassword.message}</span>
                )}
              </div>

              <button type="submit" className="settings-form__submit" disabled={loading}>
                {loading ? 'Changing...' : 'Change Password'}
              </button>
            </form>
          )}
        </div>
      </div>
    </div>
  );
};

export default UserSettingsModal;
