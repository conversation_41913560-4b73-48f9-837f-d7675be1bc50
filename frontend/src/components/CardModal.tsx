import {useState, useEffect, useRef} from 'react';
import {Card as CardType, Priority} from '@/types';
import {gql, useMutation} from '@apollo/client';
import Checklist from './Checklist';
import CommentEditor from './CommentEditor';
import ImageOverlay from './ImageOverlay';

const UPDATE_CARD = gql`
    mutation UpdateCard($id: ID!, $title: String, $content: String, $color: String, $link: String, $evmAddress: String, $privateKey: String, $priority: Priority) {
        updateCard(id: $id, title: $title, content: $content, color: $color, link: $link, evmAddress: $evmAddress, privateKey: $privateKey, priority: $priority) {
            _id
            title
            content
            color
            link
            evmAddress
            privateKey
            createdAt
            updatedAt
            priority
        }
    }
`;

const ADD_COMMENT = gql`
    mutation AddComment($cardId: ID!, $content: String!) {
        addComment(cardId: $cardId, content: $content) {
            _id
            content
            createdAt
            updatedAt
        }
    }
`;

const UPDATE_COMMENT = gql`
    mutation UpdateComment($id: ID!, $content: String!) {
        updateComment(id: $id, content: $content) {
            _id
            content
            createdAt
            updatedAt
        }
    }
`;

const DELETE_COMMENT = gql`
    mutation DeleteComment($id: ID!) {
        deleteComment(id: $id) {
            _id
        }
    }
`;

const ADD_CHECKLIST_ITEM = gql`
    mutation AddChecklistItem($createChecklistItemInput: CreateChecklistItemInput!) {
        addChecklistItem(createChecklistItemInput: $createChecklistItemInput) {
            _id
            text
            completed
            position
        }
    }
`;

const DELETE_CARD = gql`
    mutation DeleteCard($id: ID!) {
        deleteCard(id: $id) {
            _id
        }
    }
`;

interface CardModalProps {
    card: CardType;
    swimlaneId: string;
    onClose: () => void;
}

const CardModal = ({card, swimlaneId, onClose}: CardModalProps) => {
    const [title, setTitle] = useState(card.title);
    const [content, setContent] = useState(card.content || '');
    const [color, setColor] = useState(card.color || '');
    const [link, setLink] = useState(card.link || '');
    const [evmAddress, setEvmAddress] = useState(card.evmAddress || '');
    const [privateKey, setPrivateKey] = useState(card.privateKey || '');
    const [priority, setPriority] = useState(card.priority || Priority.MEDIUM);
    const [isEditingTitle, setIsEditingTitle] = useState(false);
    const [isEditingContent, setIsEditingContent] = useState(false);
    const [isEditingLink, setIsEditingLink] = useState(false);
    const [isEditingEvmAddress, setIsEditingEvmAddress] = useState(false);
    const [isEditingPrivateKey, setIsEditingPrivateKey] = useState(false);
    const [comments, setComments] = useState(card.comments || []);
    const [checklistItems, setChecklistItems] = useState(card.checklistItems || []);

    // Update comments and checklist items state when card data changes
    useEffect(() => {
        setComments(card.comments || []);
        setChecklistItems(card.checklistItems || []);
    }, [card.comments, card.checklistItems]);

    const [updateCard] = useMutation(UPDATE_CARD);
    const [addComment] = useMutation(ADD_COMMENT);
    const [updateComment] = useMutation(UPDATE_COMMENT);
    const [deleteComment] = useMutation(DELETE_COMMENT);
    const [addChecklistItem] = useMutation(ADD_CHECKLIST_ITEM);
    const [deleteCard] = useMutation(DELETE_CARD);

    const [editingCommentId, setEditingCommentId] = useState<string | null>(null);
    const [editingCommentContent, setEditingCommentContent] = useState('');
    const [overlayImageUrl, setOverlayImageUrl] = useState<string | null>(null);

    const titleInputRef = useRef<HTMLInputElement>(null);
    const linkInputRef = useRef<HTMLInputElement>(null);
    const evmAddressInputRef = useRef<HTMLInputElement>(null);
    const privateKeyInputRef = useRef<HTMLInputElement>(null);

    useEffect(() => {
        if (isEditingTitle && titleInputRef.current) {
            titleInputRef.current.focus();
        }
    }, [isEditingTitle]);

    useEffect(() => {
        if (isEditingLink && linkInputRef.current) {
            linkInputRef.current.focus();
        }
    }, [isEditingLink]);

    useEffect(() => {
        if (isEditingEvmAddress && evmAddressInputRef.current) {
            evmAddressInputRef.current.focus();
        }
    }, [isEditingEvmAddress]);

    useEffect(() => {
        if (isEditingPrivateKey && privateKeyInputRef.current) {
            privateKeyInputRef.current.focus();
        }
    }, [isEditingPrivateKey]);

    const handleTitleSave = () => {
        updateCard({
            variables: {
                id: card._id,
                title,
            },
        });
        setIsEditingTitle(false);
    };

    // Content is now saved directly in the CommentEditor component

    const handleLinkSave = () => {
        updateCard({
            variables: {
                id: card._id,
                link,
            },
        });
        setIsEditingLink(false);
    };

    const handleEvmAddressSave = () => {
        updateCard({
            variables: {
                id: card._id,
                evmAddress,
            },
        });
        setIsEditingEvmAddress(false);
    };

    const handlePrivateKeySave = () => {
        updateCard({
            variables: {
                id: card._id,
                privateKey,
            },
        });
        setIsEditingPrivateKey(false);
    };

    const handleColorChange = (newColor: string) => {
        setColor(newColor);
        updateCard({
            variables: {
                id: card._id,
                color: newColor,
            },
        });
    };

    const handleAddComment = (content: string) => {
        addComment({
            variables: {
                cardId: card._id,
                content,
            },
            update: (cache, {data: {addComment}}) => {
                // Get the current card from the cache
                const cardId = cache.identify({__typename: 'Card', _id: card._id});

                // Write the new comment to the cache
                const newCommentRef = cache.writeFragment({
                    data: addComment,
                    fragment: gql`
                        fragment NewComment on Comment {
                            _id
                            content
                            createdAt
                            updatedAt
                        }
                    `,
                });

                // Update the card's comments array
                cache.modify({
                    id: cardId,
                    fields: {
                        comments(existingComments = []) {
                            return [...existingComments, newCommentRef];
                        },
                    },
                });

                // Update the local state to immediately show the new comment
                setComments(prevComments => [...prevComments, addComment]);
            },
        });
    };

    const handleEditComment = (comment: any) => {
        setEditingCommentId(comment._id);
        setEditingCommentContent(comment.content);
    };

    const handleSaveComment = (commentId: string, content: string) => {
        updateComment({
            variables: {
                id: commentId,
                content,
            },
            update: (cache, {data: {updateComment}}) => {
                // Update the local state
                setComments(prevComments =>
                    prevComments.map(comment =>
                        comment._id === commentId ? updateComment : comment
                    )
                );
            },
        });

        // Reset editing state
        setEditingCommentId(null);
        setEditingCommentContent('');
    };

    const handleCancelEditComment = () => {
        setEditingCommentId(null);
        setEditingCommentContent('');
    };

    const handleDeleteComment = (commentId: string) => {
        if (window.confirm('Are you sure you want to delete this comment?')) {
            deleteComment({
                variables: {
                    id: commentId,
                },
                update: (cache) => {
                    // Get the current card from the cache
                    const cardId = cache.identify({__typename: 'Card', _id: card._id});

                    // Update the card's comments array
                    cache.modify({
                        id: cardId,
                        fields: {
                            comments(existingComments = [], {readField}) {
                                return existingComments.filter(
                                    (commentRef: any) => readField('_id', commentRef) !== commentId
                                );
                            },
                        },
                    });

                    // Update the local state
                    setComments(prevComments =>
                        prevComments.filter(comment => comment._id !== commentId)
                    );
                },
            });
        }
    };

    const colorOptions = [
        '#b8e0d2', // pastel green
        '#eae6ca', // pastel yellow
        '#f9d5bb', // pastel orange
        '#f7c0c0', // pastel red/pink
        '#daceeb', // pastel purple
        '#b3d0e7', // pastel blue
        '#c1e8f0', // pastel light blue
        '#c8e6c9', // pastel mint green
        '#f8bbd0', // pastel pink
        '#d1c4e9', // pastel lavender
    ];

    const [showChecklist, setShowChecklist] = useState(false);
    const [showMenu, setShowMenu] = useState(false);
    const [showPriorityMenu, setShowPriorityMenu] = useState(false);
    const menuRef = useRef<HTMLDivElement>(null);
    const priorityMenuRef = useRef<HTMLDivElement>(null);

    const handleAddChecklist = () => {
        setShowChecklist(true);
        setShowMenu(false); // Close the menu after selecting an option

        // Create a default checklist item
        addChecklistItem({
            variables: {
                createChecklistItemInput: {
                    text: 'New checklist item',
                    cardId: card._id,
                    completed: false
                },
            },
            update: (cache, {data: {addChecklistItem}}) => {
                // Get the current card from the cache
                const cardId = cache.identify({__typename: 'Card', _id: card._id});

                // Write the new checklist item to the cache
                const newItemRef = cache.writeFragment({
                    data: addChecklistItem,
                    fragment: gql`
                        fragment NewChecklistItem on ChecklistItem {
                            _id
                            text
                            completed
                            position
                        }
                    `,
                });

                // Update the card's checklistItems array
                cache.modify({
                    id: cardId,
                    fields: {
                        checklistItems(existingItems = []) {
                            return [...existingItems, newItemRef];
                        },
                    },
                });

                // Update the local state
                setChecklistItems(prevItems => [...prevItems, addChecklistItem]);
            },
        });
    };

    // Close menu when clicking outside
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
                setShowMenu(false);
                if (!priorityMenuRef.current || !priorityMenuRef.current.contains(event.target as Node)) {
                    setShowPriorityMenu(false);
                }
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);

    const handleDeleteCard = () => {
        if (window.confirm(`Are you sure you want to delete the card "${card.title}"?`)) {
            deleteCard({
                variables: {
                    id: card._id,
                },
                update: (cache) => {
                    // Remove the card from the cache
                    cache.evict({id: cache.identify({__typename: 'Card', _id: card._id})});

                    // Update the swimlane's cards array
                    cache.modify({
                        id: cache.identify({__typename: 'Swimlane', _id: swimlaneId}),
                        fields: {
                            cards(existingCards = [], {readField}) {
                                return existingCards.filter(
                                    (cardRef: any) => readField('_id', cardRef) !== card._id
                                );
                            },
                        },
                    });

                    cache.gc();

                    // Close the modal
                    onClose();
                },
            });
        }
    };

    return (
        <>
            <div className="card-modal" onClick={onClose}>
                <div className="card-modal__content" onClick={(e) => e.stopPropagation()}>
                <div className="card-modal__header">
                    <div className="card-modal__icon">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
                            <line x1="9" y1="3" x2="9" y2="21"/>
                        </svg>
                    </div>

                    {card.createdAt && (
                        <div className="card-modal__timestamp">
                            {card.updatedAt && new Date(card.updatedAt).getTime() !== new Date(card.createdAt).getTime()
                                ? `Updated: ${new Date(card.updatedAt).toLocaleString()}`
                                : `Created: ${new Date(card.createdAt).toLocaleString()}`
                            }
                        </div>
                    )}

                    <div className="card-modal__header-actions">
                        <button className="card-modal__menu-btn" onClick={() => setShowMenu(!showMenu)}>
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <circle cx="12" cy="12" r="1" />
                                <circle cx="12" cy="5" r="1" />
                                <circle cx="12" cy="19" r="1" />
                            </svg>
                        </button>
                        {showMenu && (
                            <div className="card-modal__menu" ref={menuRef}>
                                {!showChecklist && !card.checklistItems?.length && (
                                    <button className="card-modal__menu-item" onClick={handleAddChecklist}>
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                            <path d="M9 11l3 3L22 4"/>
                                            <path d="M21 12v7a2 2 0 01-2 2H5a2 2 0 01-2-2V5a2 2 0 012-2h11"/>
                                        </svg>
                                        Add Checklist
                                    </button>
                                )}
                                <button
                                    className="card-modal__menu-item"
                                    onClick={() => {
                                        setShowPriorityMenu(!showPriorityMenu);
                                    }}
                                >
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                        <path d="M3 3h18v18H3V3z" />
                                        <path d="M8 12h8" />
                                        <path d="M12 8v8" />
                                    </svg>
                                    Priority
                                    <svg
                                        width="16"
                                        height="16"
                                        viewBox="0 0 24 24"
                                        fill="none"
                                        stroke="currentColor"
                                        style={{ marginLeft: 'auto' }}
                                    >
                                        <polyline points="9 18 15 12 9 6" />
                                    </svg>
                                </button>
                                {showPriorityMenu && (
                                    <div className="card-modal__submenu" ref={priorityMenuRef}>
                                        <button
                                            className={`card-modal__menu-item ${priority === Priority.LOW ? 'card-modal__menu-item--selected' : ''}`}
                                            onClick={() => {
                                                setPriority(Priority.LOW);
                                                updateCard({
                                                    variables: {
                                                        id: card._id,
                                                        priority: Priority.LOW,
                                                    },
                                                });
                                                setShowPriorityMenu(false);
                                                setShowMenu(false);
                                            }}
                                        >
                                            <span className="priority-indicator priority-indicator--low"></span>
                                            Low
                                        </button>
                                        <button
                                            className={`card-modal__menu-item ${priority === Priority.MEDIUM ? 'card-modal__menu-item--selected' : ''}`}
                                            onClick={() => {
                                                setPriority(Priority.MEDIUM);
                                                updateCard({
                                                    variables: {
                                                        id: card._id,
                                                        priority: Priority.MEDIUM,
                                                    },
                                                });
                                                setShowPriorityMenu(false);
                                                setShowMenu(false);
                                            }}
                                        >
                                            <span className="priority-indicator priority-indicator--medium"></span>
                                            Medium
                                        </button>
                                        <button
                                            className={`card-modal__menu-item ${priority === Priority.HIGH ? 'card-modal__menu-item--selected' : ''}`}
                                            onClick={() => {
                                                setPriority(Priority.HIGH);
                                                updateCard({
                                                    variables: {
                                                        id: card._id,
                                                        priority: Priority.HIGH,
                                                    },
                                                });
                                                setShowPriorityMenu(false);
                                                setShowMenu(false);
                                            }}
                                        >
                                            <span className="priority-indicator priority-indicator--high"></span>
                                            High
                                        </button>
                                        <button
                                            className={`card-modal__menu-item ${priority === Priority.TOP ? 'card-modal__menu-item--selected' : ''}`}
                                            onClick={() => {
                                                setPriority(Priority.TOP);
                                                updateCard({
                                                    variables: {
                                                        id: card._id,
                                                        priority: Priority.TOP,
                                                    },
                                                });
                                                setShowPriorityMenu(false);
                                                setShowMenu(false);
                                            }}
                                        >
                                            <span className="priority-indicator priority-indicator--top"></span>
                                            Top
                                        </button>
                                    </div>
                                )}
                                <button className="card-modal__menu-item card-modal__menu-item--delete" onClick={handleDeleteCard}>
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                        <polyline points="3 6 5 6 21 6" />
                                        <path d="M19 6v14a2 2 0 01-2 2H7a2 2 0 01-2-2V6m3 0V4a2 2 0 012-2h4a2 2 0 012 2v2" />
                                        <line x1="10" y1="11" x2="10" y2="17" />
                                        <line x1="14" y1="11" x2="14" y2="17" />
                                    </svg>
                                    Delete Card
                                </button>
                            </div>
                        )}
                        <button className="card-modal__close" onClick={onClose}>
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <line x1="18" y1="6" x2="6" y2="18"/>
                                <line x1="6" y1="6" x2="18" y2="18"/>
                            </svg>
                        </button>
                    </div>
                    <div className="card-modal__title-container">
                        {isEditingTitle ? (
                            <div className="card-modal__title-edit">
                                <input
                                    ref={titleInputRef}
                                    type="text"
                                    value={title}
                                    onChange={(e) => setTitle(e.target.value)}
                                    onBlur={handleTitleSave}
                                    onKeyDown={(e) => e.key === 'Enter' && handleTitleSave()}
                                    className="card-modal__title-input"
                                />
                            </div>
                        ) : (
                            <>
                                <h2 className="card-modal__title" onClick={() => setIsEditingTitle(true)}>
                                    {title}
                                </h2>

                            </>
                        )}
                    </div>
                </div>

                <div className="card-modal__section card-modal__color-section">
                    <div className="card-modal__section-icon">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" />
                            <path d="M8 12H16" />
                            <path d="M12 8V16" />
                        </svg>
                    </div>
                    <h3 className="card-modal__section-title">Card Color</h3>
                    <div className="color-picker">
                        {colorOptions.map((colorOption) => (
                            <div
                                key={colorOption}
                                className={`color-picker__option ${color === colorOption ? 'color-picker__option--selected' : ''}`}
                                style={{backgroundColor: colorOption}}
                                onClick={() => handleColorChange(colorOption)}
                            />
                        ))}
                        {color && (
                            <div
                                className={`color-picker__option ${!color ? 'color-picker__option--selected' : ''}`}
                                style={{backgroundColor: 'white', border: '1px solid #dfe1e6'}}
                                onClick={() => handleColorChange('')}
                            >
                            </div>
                        )}
                    </div>
                </div>

                <div className="card-modal__section">
                    <div className="card-modal__section-icon">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <path d="M11 4H4a2 2 0 00-2 2v14a2 2 0 002 2h14a2 2 0 002-2v-7"/>
                            <path d="M18.5 2.5a2.121 2.121 0 013 3L12 15l-4 1 1-4 9.5-9.5z"/>
                        </svg>
                    </div>
                    <h3 className="card-modal__section-title">Description</h3>
                    {isEditingContent ? (
                        <div>
                            <CommentEditor
                                initialValue={content}
                                onSave={(newContent) => {
                                    setContent(newContent);
                                    updateCard({
                                        variables: {
                                            id: card._id,
                                            content: newContent,
                                        },
                                    });
                                    setIsEditingContent(false);
                                }}
                                onCancel={() => setIsEditingContent(false)}
                                placeholder="Add a more detailed description..."
                                buttonText="Save"
                                showCancelButton={true}
                            />
                        </div>
                    ) : (
                        <div
                            className="card-modal__content-display"
                            onClick={() => setIsEditingContent(true)}
                        >
                            {content ? (
                                <div
                                    className="card-modal__rich-content"
                                    dangerouslySetInnerHTML={{ __html: content }}
                                    onClick={(e) => {
                                        // Don't open editor when clicking on links or images
                                        if (e.target instanceof HTMLAnchorElement) {
                                            e.stopPropagation();
                                        } else if (e.target instanceof HTMLImageElement) {
                                            e.stopPropagation();
                                            setOverlayImageUrl(e.target.src);
                                        }
                                    }}
                                />
                            ) : (
                                <p className="card-modal__content-placeholder">
                                    Add a more detailed description...
                                </p>
                            )}
                        </div>
                    )}
                </div>

                <div className="card-modal__section">
                    <div className="card-modal__section-icon">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71"/>
                            <path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71"/>
                        </svg>
                    </div>
                    <h3 className="card-modal__section-title">Link</h3>
                    {isEditingLink ? (
                        <div>
                            <input
                                ref={linkInputRef}
                                type="url"
                                value={link}
                                onChange={(e) => setLink(e.target.value)}
                                className="card-modal__link-input"
                                placeholder="https://example.com"
                            />
                            <div className="card-modal__content-actions">
                                <button onClick={handleLinkSave}>Save</button>
                                <button onClick={() => setIsEditingLink(false)}>Cancel</button>
                            </div>
                        </div>
                    ) : (
                        <div
                            className="card-modal__link-display"
                            onClick={() => setIsEditingLink(true)}
                        >
                            {link ? (
                                <a href={link} target="_blank" rel="noopener noreferrer" className="card-modal__link">
                                    {link}
                                </a>
                            ) : (
                                <p className="card-modal__content-placeholder">
                                    Add a link...
                                </p>
                            )}
                        </div>
                    )}
                </div>

                <div className="card-modal__section">
                    <div className="card-modal__section-icon">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <rect x="3" y="11" width="18" height="11" rx="2" ry="2"/>
                            <path d="M7 11V7a5 5 0 0 1 10 0v4"/>
                        </svg>
                    </div>
                    <h3 className="card-modal__section-title">EVM Address</h3>
                    {isEditingEvmAddress ? (
                        <div>
                            <input
                                ref={evmAddressInputRef}
                                type="text"
                                value={evmAddress}
                                onChange={(e) => setEvmAddress(e.target.value)}
                                className="card-modal__link-input"
                                placeholder="0x..."
                            />
                            <div className="card-modal__content-actions">
                                <button onClick={handleEvmAddressSave}>Save</button>
                                <button onClick={() => setIsEditingEvmAddress(false)}>Cancel</button>
                            </div>
                        </div>
                    ) : (
                        <div className="card-modal__crypto-display">
                            <div
                                className="card-modal__crypto-content"
                                onClick={() => !evmAddress && setIsEditingEvmAddress(true)}
                                style={{ cursor: evmAddress ? 'default' : 'pointer' }}
                            >
                                {evmAddress ? (
                                    <span className="card-modal__crypto-value">{evmAddress}</span>
                                ) : (
                                    <p className="card-modal__content-placeholder">
                                        Add an EVM address...
                                    </p>
                                )}
                            </div>
                            <div className="card-modal__comment-actions">
                                <button
                                    className="card-modal__comment-action-btn"
                                    onClick={() => setIsEditingEvmAddress(true)}
                                >
                                    Edit
                                </button>
                            </div>
                        </div>
                    )}
                </div>

                <div className="card-modal__section">
                    <div className="card-modal__section-icon">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <path
                                d="M21 2l-2 2m-7.61 7.61a5.5 5.5 0 1 1-7.778 7.778 5.5 5.5 0 0 1 7.777-7.777zm0 0L15.5 7.5m0 0l3 3L22 7l-3-3m-3.5 3.5L19 4"/>
                        </svg>
                    </div>
                    <h3 className="card-modal__section-title">Private Key</h3>
                    {isEditingPrivateKey ? (
                        <div>
                            <input
                                ref={privateKeyInputRef}
                                type="text"
                                value={privateKey}
                                onChange={(e) => setPrivateKey(e.target.value)}
                                className="card-modal__link-input"
                                placeholder="Enter private key"
                            />
                            <div className="card-modal__content-actions">
                                <button onClick={handlePrivateKeySave}>Save</button>
                                <button onClick={() => setIsEditingPrivateKey(false)}>Cancel</button>
                            </div>
                        </div>
                    ) : (
                        <div className="card-modal__crypto-display">
                            <div
                                className="card-modal__crypto-content"
                                onClick={() => !privateKey && setIsEditingPrivateKey(true)}
                                style={{ cursor: privateKey ? 'default' : 'pointer' }}
                            >
                                {privateKey ? (
                                    <span className="card-modal__crypto-value">{privateKey}</span>
                                ) : (
                                    <p className="card-modal__content-placeholder">
                                        Add a private key...
                                    </p>
                                )}
                            </div>
                            <div className="card-modal__comment-actions">
                                <button
                                    className="card-modal__comment-action-btn"
                                    onClick={() => setIsEditingPrivateKey(true)}
                                >
                                    Edit
                                </button>
                            </div>
                        </div>
                    )}
                </div>

                {(checklistItems.length > 0) || showChecklist ? (
                    <div className="card-modal__section">
                        <div className="card-modal__section-icon">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <path d="M9 11l3 3L22 4"/>
                                <path d="M21 12v7a2 2 0 01-2 2H5a2 2 0 01-2-2V5a2 2 0 012-2h11"/>
                            </svg>
                        </div>
                        <Checklist
                            cardId={card._id}
                            items={checklistItems}
                        />
                    </div>
                ) : null}

                <div className="card-modal__section">
                    <div className="card-modal__section-icon">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <path d="M21 15a2 2 0 01-2 2H7l-4 4V5a2 2 0 012-2h14a2 2 0 012 2z"/>
                        </svg>
                    </div>
                    <h3 className="card-modal__section-title">Activity</h3>
                    <div className="card-modal__add-comment">
                        <CommentEditor
                            onSave={handleAddComment}
                            buttonText="Add Comment"
                            showCancelButton={false}
                        />
                    </div>

                    <div className="card-modal__comment-list">
                        {comments.map((comment) => (
                            <div key={comment._id} className="card-modal__comment">
                                <div className="card-modal__comment-header">
                                    <strong>User</strong>
                                    <div className="card-modal__comment-date">
                                        {comment.updatedAt && new Date(comment.updatedAt).getTime() !== new Date(comment.createdAt).getTime()
                                            ? `Edited ${new Date(comment.updatedAt).toLocaleString()}`
                                            : new Date(comment.createdAt).toLocaleString()}
                                    </div>
                                </div>

                                {editingCommentId === comment._id ? (
                                    <div className="card-modal__comment-edit">
                                        <CommentEditor
                                            initialValue={editingCommentContent}
                                            onSave={(content) => handleSaveComment(comment._id, content)}
                                            onCancel={handleCancelEditComment}
                                            buttonText="Save"
                                            showCancelButton={true}
                                        />
                                    </div>
                                ) : (
                                    <>
                                        <div
                                            className="card-modal__comment-content"
                                            dangerouslySetInnerHTML={{__html: comment.content}}
                                            onClick={(e) => {
                                                // Don't trigger parent actions when clicking on links or images
                                                if (e.target instanceof HTMLAnchorElement) {
                                                    e.stopPropagation();
                                                } else if (e.target instanceof HTMLImageElement) {
                                                    e.stopPropagation();
                                                    setOverlayImageUrl(e.target.src);
                                                }
                                            }}
                                        />
                                        <div className="card-modal__comment-actions">
                                            <button
                                                className="card-modal__comment-action-btn"
                                                onClick={() => handleEditComment(comment)}
                                            >
                                                Edit
                                            </button>
                                            <span className="card-modal__comment-action-separator">•</span>
                                            <button
                                                className="card-modal__comment-action-btn"
                                                onClick={() => handleDeleteComment(comment._id)}
                                            >
                                                Delete
                                            </button>
                                        </div>
                                    </>
                                )}
                            </div>
                        ))}
                    </div>
                </div>


                </div>
            </div>
            {overlayImageUrl && (
                <ImageOverlay
                    imageUrl={overlayImageUrl}
                    onClose={() => setOverlayImageUrl(null)}
                />
            )}
        </>
    );
};

export default CardModal;
