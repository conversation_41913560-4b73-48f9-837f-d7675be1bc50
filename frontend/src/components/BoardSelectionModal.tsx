import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { gql, useQuery, useMutation } from '@apollo/client';
import { useAuth } from '@/contexts/AuthContext';

interface BoardSelectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onBoardSelect: (boardId: string) => void;
}

interface CreateBoardFormData {
  name: string;
}

const GET_USER_BOARDS = gql`
  query GetUserBoards {
    userBoards {
      _id
      name
      createdAt
      updatedAt
    }
  }
`;

const CREATE_BOARD = gql`
  mutation CreateBoard($createBoardInput: CreateBoardInput!) {
    createBoard(createBoardInput: $createBoardInput) {
      _id
      name
      createdAt
      updatedAt
    }
  }
`;

const UPDATE_BOARD = gql`
  mutation UpdateBoard($updateBoardInput: UpdateBoardInput!) {
    updateBoard(updateBoardInput: $updateBoardInput) {
      _id
      name
      createdAt
      updatedAt
    }
  }
`;

const DELETE_BOARD = gql`
  mutation DeleteBoard($id: ID!) {
    deleteBoard(id: $id) {
      _id
      name
    }
  }
`;

const BoardSelectionModal: React.FC<BoardSelectionModalProps> = ({ isOpen, onClose, onBoardSelect }) => {
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [editingBoardId, setEditingBoardId] = useState<string | null>(null);
  const [editingBoardName, setEditingBoardName] = useState('');
  const [deletingBoardId, setDeletingBoardId] = useState<string | null>(null);

  const { user } = useAuth();

  const { data: boardsData, loading: boardsLoading, refetch } = useQuery(GET_USER_BOARDS, {
    skip: !user,
  });

  const [createBoard] = useMutation(CREATE_BOARD);
  const [updateBoard] = useMutation(UPDATE_BOARD);
  const [deleteBoard] = useMutation(DELETE_BOARD);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<CreateBoardFormData>();

  const handleCreateBoard = async (data: CreateBoardFormData) => {
    setLoading(true);
    setError(null);
    try {
      const { data: createData } = await createBoard({
        variables: {
          createBoardInput: { name: data.name },
        },
      });

      if (createData?.createBoard) {
        await refetch();
        onBoardSelect(createData.createBoard._id);
        setShowCreateForm(false);
        reset();
        onClose();
      }
    } catch (err: any) {
      setError(err.message || 'Failed to create board');
    } finally {
      setLoading(false);
    }
  };

  const handleBoardClick = (boardId: string) => {
    onBoardSelect(boardId);
    onClose();
  };

  const handleStartEdit = (boardId: string, currentName: string) => {
    setEditingBoardId(boardId);
    setEditingBoardName(currentName);
  };

  const handleCancelEdit = () => {
    setEditingBoardId(null);
    setEditingBoardName('');
  };

  const handleSaveEdit = async () => {
    if (!editingBoardId || !editingBoardName.trim()) return;

    setLoading(true);
    setError(null);
    try {
      await updateBoard({
        variables: {
          updateBoardInput: {
            id: editingBoardId,
            name: editingBoardName.trim(),
          },
        },
      });

      await refetch();
      setEditingBoardId(null);
      setEditingBoardName('');
    } catch (err: any) {
      setError(err.message || 'Failed to update board');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteBoard = async (boardId: string) => {
    setDeletingBoardId(boardId);
  };

  const handleConfirmDelete = async () => {
    if (!deletingBoardId) return;

    setLoading(true);
    setError(null);
    try {
      await deleteBoard({
        variables: {
          id: deletingBoardId,
        },
      });

      await refetch();
      setDeletingBoardId(null);
    } catch (err: any) {
      setError(err.message || 'Failed to delete board');
    } finally {
      setLoading(false);
    }
  };

  const handleCancelDelete = () => {
    setDeletingBoardId(null);
  };

  if (!isOpen) return null;

  return (
    <div className="board-selection-modal-overlay" onClick={onClose}>
      <div className="board-selection-modal" onClick={(e) => e.stopPropagation()}>
        <div className="board-selection-modal__header">
          <h2>Select Board</h2>
          <button className="board-selection-modal__close" onClick={onClose}>
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path d="M18 6L6 18M6 6l12 12" />
            </svg>
          </button>
        </div>

        <div className="board-selection-modal__content">
          {error && <div className="board-selection-modal__error">{error}</div>}

          {!showCreateForm && (
            <>
              <div className="board-selection-modal__actions">
                <button
                  className="board-selection-modal__create-btn"
                  onClick={() => setShowCreateForm(true)}
                >
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <path d="M12 5v14M5 12h14" />
                  </svg>
                  Create New Board
                </button>
              </div>

              <div className="board-list">
                {boardsLoading ? (
                  <div className="board-list__loading">Loading boards...</div>
                ) : boardsData?.userBoards?.length > 0 ? (
                  boardsData.userBoards.map((board: any) => (
                    <div key={board._id} className="board-item">
                      {editingBoardId === board._id ? (
                        <div className="board-item__edit">
                          <input
                            type="text"
                            value={editingBoardName}
                            onChange={(e) => setEditingBoardName(e.target.value)}
                            className="board-item__edit-input"
                            onKeyDown={(e) => {
                              if (e.key === 'Enter') {
                                handleSaveEdit();
                              } else if (e.key === 'Escape') {
                                handleCancelEdit();
                              }
                            }}
                            autoFocus
                          />
                          <div className="board-item__edit-actions">
                            <button
                              className="board-item__edit-save"
                              onClick={handleSaveEdit}
                              disabled={loading}
                            >
                              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <path d="M20 6L9 17l-5-5" />
                              </svg>
                            </button>
                            <button
                              className="board-item__edit-cancel"
                              onClick={handleCancelEdit}
                              disabled={loading}
                            >
                              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <path d="M18 6L6 18M6 6l12 12" />
                              </svg>
                            </button>
                          </div>
                        </div>
                      ) : (
                        <>
                          <div
                            className="board-item__info"
                            onClick={() => handleBoardClick(board._id)}
                          >
                            <h3 className="board-item__name">{board.name}</h3>
                            <p className="board-item__date">
                              Created: {new Date(board.createdAt).toLocaleDateString()}
                            </p>
                          </div>
                          <div className="board-item__actions">
                            <button
                              className="board-item__action-btn board-item__edit-btn"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleStartEdit(board._id, board.name);
                              }}
                              title="Rename board"
                            >
                              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7" />
                                <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z" />
                              </svg>
                            </button>
                            <button
                              className="board-item__action-btn board-item__delete-btn"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleDeleteBoard(board._id);
                              }}
                              title="Delete board"
                            >
                              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <path d="M3 6h18M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2" />
                                <line x1="10" y1="11" x2="10" y2="17" />
                                <line x1="14" y1="11" x2="14" y2="17" />
                              </svg>
                            </button>
                            <div className="board-item__arrow">
                              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <path d="M9 18l6-6-6-6" />
                              </svg>
                            </div>
                          </div>
                        </>
                      )}
                    </div>
                  ))
                ) : (
                  <div className="board-list__empty">
                    <p>No boards found. Create your first board!</p>
                  </div>
                )}
              </div>
            </>
          )}

          {showCreateForm && (
            <div className="create-board-form">
              <div className="create-board-form__header">
                <h3>Create New Board</h3>
                <button
                  className="create-board-form__back"
                  onClick={() => {
                    setShowCreateForm(false);
                    setError(null);
                    reset();
                  }}
                >
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <path d="M15 18l-6-6 6-6" />
                  </svg>
                  Back
                </button>
              </div>

              <form onSubmit={handleSubmit(handleCreateBoard)} className="create-board-form__form">
                <div className="create-board-form__field">
                  <label htmlFor="name">Board Name</label>
                  <input
                    type="text"
                    id="name"
                    placeholder="Enter board name..."
                    {...register('name', { required: 'Board name is required' })}
                  />
                  {errors.name && (
                    <span className="create-board-form__error">{errors.name.message}</span>
                  )}
                </div>

                <button type="submit" className="create-board-form__submit" disabled={loading}>
                  {loading ? 'Creating...' : 'Create Board'}
                </button>
              </form>
            </div>
          )}
        </div>

        {/* Delete Confirmation Dialog */}
        {deletingBoardId && (
          <div className="board-delete-confirmation">
            <div className="board-delete-confirmation__content">
              <h3>Delete Board</h3>
              <p>Are you sure you want to delete this board? This action cannot be undone.</p>
              <div className="board-delete-confirmation__actions">
                <button
                  className="board-delete-confirmation__cancel"
                  onClick={handleCancelDelete}
                  disabled={loading}
                >
                  Cancel
                </button>
                <button
                  className="board-delete-confirmation__confirm"
                  onClick={handleConfirmDelete}
                  disabled={loading}
                >
                  {loading ? 'Deleting...' : 'Delete'}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default BoardSelectionModal;
