import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { gql, useQuery, useMutation } from '@apollo/client';
import { useAuth } from '@/contexts/AuthContext';

interface BoardSelectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onBoardSelect: (boardId: string) => void;
}

interface CreateBoardFormData {
  name: string;
}

const GET_USER_BOARDS = gql`
  query GetUserBoards {
    userBoards {
      _id
      name
      createdAt
      updatedAt
    }
  }
`;

const CREATE_BOARD = gql`
  mutation CreateBoard($createBoardInput: CreateBoardInput!) {
    createBoard(createBoardInput: $createBoardInput) {
      _id
      name
      createdAt
      updatedAt
    }
  }
`;

const BoardSelectionModal: React.FC<BoardSelectionModalProps> = ({ isOpen, onClose, onBoardSelect }) => {
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const { user } = useAuth();

  const { data: boardsData, loading: boardsLoading, refetch } = useQuery(GET_USER_BOARDS, {
    skip: !user,
  });

  const [createBoard] = useMutation(CREATE_BOARD);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<CreateBoardFormData>();

  const handleCreateBoard = async (data: CreateBoardFormData) => {
    setLoading(true);
    setError(null);
    try {
      const { data: createData } = await createBoard({
        variables: {
          createBoardInput: { name: data.name },
        },
      });

      if (createData?.createBoard) {
        await refetch();
        onBoardSelect(createData.createBoard._id);
        setShowCreateForm(false);
        reset();
        onClose();
      }
    } catch (err: any) {
      setError(err.message || 'Failed to create board');
    } finally {
      setLoading(false);
    }
  };

  const handleBoardClick = (boardId: string) => {
    onBoardSelect(boardId);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="board-selection-modal-overlay" onClick={onClose}>
      <div className="board-selection-modal" onClick={(e) => e.stopPropagation()}>
        <div className="board-selection-modal__header">
          <h2>Select Board</h2>
          <button className="board-selection-modal__close" onClick={onClose}>
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path d="M18 6L6 18M6 6l12 12" />
            </svg>
          </button>
        </div>

        <div className="board-selection-modal__content">
          {error && <div className="board-selection-modal__error">{error}</div>}

          {!showCreateForm && (
            <>
              <div className="board-selection-modal__actions">
                <button
                  className="board-selection-modal__create-btn"
                  onClick={() => setShowCreateForm(true)}
                >
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <path d="M12 5v14M5 12h14" />
                  </svg>
                  Create New Board
                </button>
              </div>

              <div className="board-list">
                {boardsLoading ? (
                  <div className="board-list__loading">Loading boards...</div>
                ) : boardsData?.userBoards?.length > 0 ? (
                  boardsData.userBoards.map((board: any) => (
                    <div
                      key={board._id}
                      className="board-item"
                      onClick={() => handleBoardClick(board._id)}
                    >
                      <div className="board-item__info">
                        <h3 className="board-item__name">{board.name}</h3>
                        <p className="board-item__date">
                          Created: {new Date(board.createdAt).toLocaleDateString()}
                        </p>
                      </div>
                      <div className="board-item__arrow">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                          <path d="M9 18l6-6-6-6" />
                        </svg>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="board-list__empty">
                    <p>No boards found. Create your first board!</p>
                  </div>
                )}
              </div>
            </>
          )}

          {showCreateForm && (
            <div className="create-board-form">
              <div className="create-board-form__header">
                <h3>Create New Board</h3>
                <button
                  className="create-board-form__back"
                  onClick={() => {
                    setShowCreateForm(false);
                    setError(null);
                    reset();
                  }}
                >
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <path d="M15 18l-6-6 6-6" />
                  </svg>
                  Back
                </button>
              </div>

              <form onSubmit={handleSubmit(handleCreateBoard)} className="create-board-form__form">
                <div className="create-board-form__field">
                  <label htmlFor="name">Board Name</label>
                  <input
                    type="text"
                    id="name"
                    placeholder="Enter board name..."
                    {...register('name', { required: 'Board name is required' })}
                  />
                  {errors.name && (
                    <span className="create-board-form__error">{errors.name.message}</span>
                  )}
                </div>

                <button type="submit" className="create-board-form__submit" disabled={loading}>
                  {loading ? 'Creating...' : 'Create Board'}
                </button>
              </form>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default BoardSelectionModal;
