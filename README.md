# CryptoDo - Trello-like Application

A modern Trello-like application built with Next.js, NestJS, MongoDB, and GraphQL.

## Features

- Drag and drop cards between swimlanes
- Card priorities (Low, Medium, High, Top)
- Comments with WYSIWYG editor
- Checklists with completion tracking
- EVM addresses and private keys
- HTTP links
- Export/Import functionality
- Card sorting by name or priority
- Professional UI design
- Card timestamps (created/updated)

## Technology Stack

- **Frontend**: Next.js with TypeScript, Apollo Client, React Beautiful DnD
- **Backend**: NestJS with TypeScript, GraphQL, Apollo Server
- **Database**: MongoDB
- **Package Manager**: pnpm
- **Containerization**: Docker and Docker Compose

## Getting Started

### Prerequisites

- Docker and Docker Compose installed on your machine
- For local development: Node.js 20+ (LTS) and pnpm

### Local Development with Docker

1. Clone the repository
2. Navigate to the project directory
3. Run the following command to start all services:

```bash
docker compose up -d
```

4. Access the application:
   - Frontend: http://localhost:4545
   - Backend GraphQL Playground: http://localhost:4544/graphql
   - MongoDB: localhost:27018

5. To stop the application:

```bash
docker compose down
```

6. To rebuild images:

```bash
docker compose build --no-cache
```

### Production Deployment (dsserv.de)

1. **Quick deployment using the deployment script:**

```bash
./deploy-server.sh
```

2. **Manual deployment:**

```bash
docker compose -f docker-compose.prod.yml up -d --build
```

3. **Access the production application:**
   - Frontend: https://cryptodo.dsserv.de
   - Backend GraphQL API: https://cryptodo.dsserv.de/graphql

4. **Production management:**

```bash
# View logs
docker compose -f docker-compose.prod.yml logs -f

# Stop services
docker compose -f docker-compose.prod.yml down

# Restart services
docker compose -f docker-compose.prod.yml restart
```

### Local Development

#### Frontend

```bash
cd frontend
pnpm install
pnpm dev
```

#### Backend

```bash
cd backend
pnpm install
pnpm start:dev
```

#### MongoDB

Make sure MongoDB is running locally on port 27017, or update the connection string in the backend configuration.

## Project Structure

```
cryptodo/
├── frontend/                 # Next.js frontend application
│   ├── src/
│   │   ├── components/      # React components
│   │   ├── styles/          # SCSS styles
│   │   └── pages/           # Next.js pages
│   ├── public/              # Static assets
│   ├── Dockerfile           # Frontend Docker configuration
│   ├── .dockerignore        # Docker ignore file
│   └── package.json         # Frontend dependencies
│
├── backend/                  # NestJS backend application
│   ├── src/
│   │   ├── card/           # Card module (GraphQL resolvers, services)
│   │   ├── comment/        # Comment module
│   │   ├── checklist/      # Checklist module
│   │   ├── swimlane/       # Swimlane module
│   │   ├── board/          # Board module
│   │   └── main.ts         # Application entry point
│   ├── Dockerfile          # Backend Docker configuration
│   ├── .dockerignore       # Docker ignore file
│   └── package.json        # Backend dependencies
│
├── docker-compose.yml       # Local development configuration
├── docker-compose.prod.yml  # Production deployment configuration
├── deploy-server.sh         # Production deployment script
├── nginx.conf              # Optional reverse proxy configuration
└── README.md               # Project documentation
```

## Environment Variables

### Local Development

**Backend (.env)**
```
MONGODB_URI=mongodb://mongodb:27017/cryptodo
FRONTEND_URL=http://localhost:4545
NODE_ENV=development
```

**Frontend (.env.local)**
```
NEXT_PUBLIC_GRAPHQL_URL=http://localhost:4544/graphql
NODE_ENV=development
```

### Production (dsserv.de)

**Backend (.env.production)**
```
MONGODB_URI=mongodb://mongodb:27017/cryptodo
FRONTEND_URL=https://cryptodo.dsserv.de
NODE_ENV=production
PORT=4544
```

**Frontend (.env.production)**
```
NEXT_PUBLIC_GRAPHQL_URL=https://cryptodo.dsserv.de/graphql
NODE_ENV=production
```

## CORS Configuration

The backend is configured to accept requests from:
- `http://localhost:4545` (local development)
- `https://cryptodo.dsserv.de` (production frontend)
- `http://cryptodo.dsserv.de` (production frontend without SSL)

CORS is automatically configured based on the `FRONTEND_URL` environment variable and includes additional allowed origins for flexibility.

## GraphQL API

The GraphQL API is available at `/graphql` endpoint. Key operations include:

- `board`: Get the main board with swimlanes and cards
- `createCard`: Create a new card
- `updateCard`: Update card properties (including priority)
- `deleteCard`: Delete a card
- `createComment`: Add a comment to a card
- `createChecklistItem`: Add a checklist item to a card
- `sortCards`: Sort cards by name or priority

## License

This project is licensed under the MIT License.
