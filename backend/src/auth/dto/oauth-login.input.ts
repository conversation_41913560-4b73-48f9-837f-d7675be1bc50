import { InputType, Field } from '@nestjs/graphql';
import { IsString, IsEmail, IsOptional } from 'class-validator';

@InputType()
export class OAuthLoginInput {
  @Field()
  @IsString()
  provider: string; // 'google' or 'apple'

  @Field()
  @IsString()
  providerId: string;

  @Field()
  @IsEmail()
  email: string;

  @Field()
  @IsString()
  name: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  avatar?: string;
}
