import { Injectable, UnauthorizedException, ConflictException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { UserService } from '../user/user.service';
import { User } from '../user/schemas/user.schema';
import { RegisterInput } from './dto/register.input';
import { LoginInput } from './dto/login.input';
import { OAuthLoginInput } from './dto/oauth-login.input';
import { AuthResponse } from './dto/auth-response';

@Injectable()
export class AuthService {
  constructor(
    private userService: UserService,
    private jwtService: JwtService,
  ) {}

  async register(registerInput: RegisterInput): Promise<AuthResponse> {
    const user = await this.userService.create(registerInput);
    const accessToken = this.generateAccessToken(user);
    
    return {
      accessToken,
      user,
    };
  }

  async login(loginInput: LoginInput): Promise<AuthResponse> {
    const user = await this.userService.findByEmail(loginInput.email);
    
    if (!user || !user.isActive) {
      throw new UnauthorizedException('Invalid credentials');
    }

    const isPasswordValid = await this.userService.validatePassword(user, loginInput.password);
    if (!isPasswordValid) {
      throw new UnauthorizedException('Invalid credentials');
    }

    const accessToken = this.generateAccessToken(user);
    
    return {
      accessToken,
      user,
    };
  }

  async oauthLogin(oauthInput: OAuthLoginInput): Promise<AuthResponse> {
    let user: User | null = null;

    // Try to find existing user by provider ID
    if (oauthInput.provider === 'google') {
      user = await this.userService.findByGoogleId(oauthInput.providerId);
    } else if (oauthInput.provider === 'apple') {
      user = await this.userService.findByAppleId(oauthInput.providerId);
    }

    // If not found by provider ID, try to find by email
    if (!user) {
      user = await this.userService.findByEmail(oauthInput.email);
      
      if (user) {
        // Link the OAuth provider to existing account
        user = await this.userService.linkOAuthProvider(
          user._id,
          oauthInput.provider,
          oauthInput.providerId
        );
      }
    }

    // If still not found, create new user
    if (!user) {
      const userData = {
        email: oauthInput.email,
        name: oauthInput.name,
        avatar: oauthInput.avatar,
        oauthProviders: [oauthInput.provider],
        emailVerified: true, // OAuth emails are considered verified
      };

      if (oauthInput.provider === 'google') {
        userData['googleId'] = oauthInput.providerId;
      } else if (oauthInput.provider === 'apple') {
        userData['appleId'] = oauthInput.providerId;
      }

      user = await this.userService.create(userData);
    }

    if (!user.isActive) {
      throw new UnauthorizedException('Account is deactivated');
    }

    const accessToken = this.generateAccessToken(user);
    
    return {
      accessToken,
      user,
    };
  }

  async requestPasswordReset(email: string): Promise<string> {
    return this.userService.createPasswordResetToken(email);
  }

  async resetPassword(token: string, newPassword: string): Promise<void> {
    return this.userService.resetPassword(token, newPassword);
  }

  private generateAccessToken(user: User): string {
    const payload = {
      sub: user._id,
      email: user.email,
      name: user.name,
    };

    return this.jwtService.sign(payload);
  }

  async validateUser(userId: string): Promise<User> {
    const user = await this.userService.findById(userId);
    if (!user || !user.isActive) {
      throw new UnauthorizedException('User not found or inactive');
    }
    return user;
  }
}
