import { Resolver, Mutation, Args, Query } from '@nestjs/graphql';
import { UseGuards } from '@nestjs/common';
import { AuthService } from './auth.service';
import { UserService } from '../user/user.service';
import { User } from '../user/schemas/user.schema';
import { AuthResponse, MessageResponse } from './dto/auth-response';
import { RegisterInput } from './dto/register.input';
import { LoginInput } from './dto/login.input';
import { OAuthLoginInput } from './dto/oauth-login.input';
import { RequestPasswordResetInput, ResetPasswordInput } from './dto/password-reset.input';
import { UpdateUserInput, ChangePasswordInput } from './dto/update-user.input';
import { JwtAuthGuard } from './guards/jwt-auth.guard';
import { CurrentUser } from './decorators/current-user.decorator';

@Resolver(() => User)
export class AuthResolver {
  constructor(
    private authService: AuthService,
    private userService: UserService,
  ) {}

  @Mutation(() => AuthResponse)
  async register(@Args('registerInput') registerInput: RegisterInput): Promise<AuthResponse> {
    return this.authService.register(registerInput);
  }

  @Mutation(() => AuthResponse)
  async login(@Args('loginInput') loginInput: LoginInput): Promise<AuthResponse> {
    return this.authService.login(loginInput);
  }

  @Mutation(() => AuthResponse)
  async oauthLogin(@Args('oauthInput') oauthInput: OAuthLoginInput): Promise<AuthResponse> {
    return this.authService.oauthLogin(oauthInput);
  }

  @Mutation(() => MessageResponse)
  async requestPasswordReset(
    @Args('requestPasswordResetInput') requestPasswordResetInput: RequestPasswordResetInput,
  ): Promise<MessageResponse> {
    await this.authService.requestPasswordReset(requestPasswordResetInput.email);
    return { message: 'Password reset email sent' };
  }

  @Mutation(() => MessageResponse)
  async resetPassword(
    @Args('resetPasswordInput') resetPasswordInput: ResetPasswordInput,
  ): Promise<MessageResponse> {
    await this.authService.resetPassword(resetPasswordInput.token, resetPasswordInput.newPassword);
    return { message: 'Password reset successfully' };
  }

  @Query(() => User)
  @UseGuards(JwtAuthGuard)
  async me(@CurrentUser() user: User): Promise<User> {
    return user;
  }

  @Mutation(() => User)
  @UseGuards(JwtAuthGuard)
  async updateProfile(
    @CurrentUser() user: User,
    @Args('updateUserInput') updateUserInput: UpdateUserInput,
  ): Promise<User> {
    return this.userService.updateUser(user._id, updateUserInput);
  }

  @Mutation(() => MessageResponse)
  @UseGuards(JwtAuthGuard)
  async changePassword(
    @CurrentUser() user: User,
    @Args('changePasswordInput') changePasswordInput: ChangePasswordInput,
  ): Promise<MessageResponse> {
    await this.userService.changePassword(
      user._id,
      changePasswordInput.currentPassword,
      changePasswordInput.newPassword,
    );
    return { message: 'Password changed successfully' };
  }
}
