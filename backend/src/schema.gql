# ------------------------------------------------------
# THIS FILE WAS AUTOMATICALLY GENERATED (DO NOT MODIFY)
# ------------------------------------------------------

type AuthResponse {
  accessToken: String!
  user: User!
}

type Board {
  _id: ID!
  createdAt: DateTime!
  name: String!
  swimlanes: [Swimlane!]!
  updatedAt: DateTime!
  userId: ID!
}

type Card {
  _id: ID!
  checklistItems: [ChecklistItem!]!
  color: String
  comments: [Comment!]!
  content: String
  createdAt: DateTime!
  evmAddress: String
  link: String
  position: Float!
  priority: Priority!
  privateKey: String
  swimlaneId: ID!
  title: String!
  updatedAt: DateTime!
}

input ChangePasswordInput {
  currentPassword: String!
  newPassword: String!
}

type ChecklistItem {
  _id: ID!
  cardId: ID!
  completed: Boolean!
  position: Float!
  text: String!
}

type Comment {
  _id: ID!
  cardId: ID!
  content: String!
  createdAt: DateTime!
  updatedAt: DateTime!
}

input CreateBoardInput {
  name: String!
}

input CreateChecklistItemInput {
  cardId: ID!
  completed: Boolean
  text: String!
}

input CreateSwimlaneInput {
  boardId: ID!
  title: String!
}

"""
A date-time string at UTC, such as 2019-12-03T09:54:33Z, compliant with the date-time format.
"""
scalar DateTime

input LoginInput {
  email: String!
  password: String!
}

type MessageResponse {
  message: String!
}

type Mutation {
  addCard(swimlaneId: ID!, title: String!): Card!
  addChecklistItem(createChecklistItemInput: CreateChecklistItemInput!): ChecklistItem!
  addComment(cardId: ID!, content: String!): Comment!
  changePassword(changePasswordInput: ChangePasswordInput!): MessageResponse!
  createBoard(createBoardInput: CreateBoardInput!): Board!
  createSwimlane(createSwimlaneInput: CreateSwimlaneInput!): Swimlane!
  deleteCard(id: ID!): Card!
  deleteComment(id: ID!): Comment!
  deleteSwimlane(id: ID!): Swimlane!
  login(loginInput: LoginInput!): AuthResponse!
  moveCard(cardId: ID!, destinationSwimlaneId: ID!, position: Float!, sourceSwimlaneId: ID!): Card!
  moveSwimlane(position: Float!, swimlaneId: ID!): Swimlane!
  oauthLogin(oauthInput: OAuthLoginInput!): AuthResponse!
  register(registerInput: RegisterInput!): AuthResponse!
  removeChecklistItem(id: ID!): ChecklistItem!
  requestPasswordReset(requestPasswordResetInput: RequestPasswordResetInput!): MessageResponse!
  resetPassword(resetPasswordInput: ResetPasswordInput!): MessageResponse!
  toggleChecklistItem(id: ID!): ChecklistItem!
  updateCard(color: String, content: String, evmAddress: String, id: ID!, link: String, priority: Priority, privateKey: String, title: String): Card!
  updateChecklistItem(updateChecklistItemInput: UpdateChecklistItemInput!): ChecklistItem!
  updateComment(content: String!, id: ID!): Comment!
  updateProfile(updateUserInput: UpdateUserInput!): User!
}

input OAuthLoginInput {
  avatar: String
  email: String!
  name: String!
  provider: String!
  providerId: String!
}

enum Priority {
  HIGH
  LOW
  MEDIUM
  TOP
}

type Query {
  board(boardId: ID): Board!
  card(id: ID!): Card!
  cards: [Card!]!
  checklistItems: [ChecklistItem!]!
  checklistItemsByCardId(cardId: ID!): [ChecklistItem!]!
  comments: [Comment!]!
  commentsByCardId(cardId: ID!): [Comment!]!
  me: User!
  swimlane(id: String!): Swimlane!
  swimlanes: [Swimlane!]!
  userBoard(boardId: ID): Board!
  userBoards: [Board!]!
}

input RegisterInput {
  email: String!
  language: String
  name: String!
  password: String!
}

input RequestPasswordResetInput {
  email: String!
}

input ResetPasswordInput {
  newPassword: String!
  token: String!
}

type Swimlane {
  _id: ID!
  boardId: ID!
  cards: [Card!]!
  position: Float!
  title: String!
}

input UpdateChecklistItemInput {
  cardId: ID
  completed: Boolean
  id: ID!
  text: String
}

input UpdateUserInput {
  avatar: String
  email: String
  language: String
  name: String
}

type User {
  _id: ID!
  avatar: String
  createdAt: DateTime!
  email: String!
  emailVerified: Boolean!
  isActive: Boolean!
  language: String
  name: String!
  oauthProviders: [String!]!
  updatedAt: DateTime!
}