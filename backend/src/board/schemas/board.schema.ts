import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';
import { Field, ID, ObjectType } from '@nestjs/graphql';
import { Swimlane } from '../../swimlane/schemas/swimlane.schema';

@ObjectType()
@Schema({ timestamps: true })
export class Board extends Document {
  @Field(() => ID)
  _id: string;

  @Field()
  @Prop({ required: true })
  name: string;

  @Field(() => ID)
  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'User', required: true })
  userId: string;

  @Field(() => [Swimlane])
  swimlanes: Swimlane[];

  @Field()
  createdAt: Date;

  @Field()
  updatedAt: Date;
}

export const BoardSchema = SchemaFactory.createForClass(Board);
