import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Board } from './schemas/board.schema';
import { CreateBoardInput } from './dto/create-board.input';
import { SwimlaneService } from '../swimlane/swimlane.service';

@Injectable()
export class BoardService {
  constructor(
    @InjectModel(Board.name) private boardModel: Model<Board>,
    private swimlaneService: SwimlaneService,
  ) {}

  async create(createBoardInput: CreateBoardInput & { userId: string }): Promise<Board> {
    const createdBoard = new this.boardModel(createBoardInput);
    return createdBoard.save();
  }

  async findAll(): Promise<Board[]> {
    return this.boardModel.find().exec();
  }

  async findByUserId(userId: string): Promise<Board[]> {
    return this.boardModel.find({ userId }).exec();
  }

  async findOne(id: string): Promise<Board> {
    const board = await this.boardModel.findById(id).exec();
    if (!board) {
      throw new NotFoundException(`Board with ID ${id} not found`);
    }
    return board;
  }

  async getDefaultBoard(userId?: string): Promise<any> {
    // Get the first board for the user or create a default one if none exists
    let board: Board | null;

    if (userId) {
      board = await this.boardModel.findOne({ userId }).exec();
    } else {
      // For backward compatibility, get any board if no user is specified
      board = await this.boardModel.findOne().exec();
    }

    if (!board && userId) {
      board = await this.create({ name: 'Default Board', userId });

      // Create default swimlanes
      await this.swimlaneService.create({ title: 'To Do', boardId: board._id });
      await this.swimlaneService.create({ title: 'In Progress', boardId: board._id });
      await this.swimlaneService.create({ title: 'Done', boardId: board._id });
    } else if (!board) {
      // Create a board without user for backward compatibility
      board = await this.create({ name: 'Default Board', userId: 'anonymous' });

      // Create default swimlanes
      await this.swimlaneService.create({ title: 'To Do', boardId: board._id });
      await this.swimlaneService.create({ title: 'In Progress', boardId: board._id });
      await this.swimlaneService.create({ title: 'Done', boardId: board._id });
    }

    // Populate swimlanes
    const swimlanes = await this.swimlaneService.findByBoardId(board._id);

    return {
      ...board.toObject(),
      swimlanes,
    };
  }

  async getUserBoard(userId: string, boardId?: string): Promise<any> {
    let board: Board | null;

    if (boardId) {
      board = await this.boardModel.findOne({ _id: boardId, userId }).exec();
      if (!board) {
        throw new NotFoundException(`Board with ID ${boardId} not found for user`);
      }
    } else {
      // Get the first board for the user
      board = await this.boardModel.findOne({ userId }).exec();
      if (!board) {
        // Create a default board for the user
        board = await this.create({ name: 'My Board', userId });

        // Create default swimlanes
        await this.swimlaneService.create({ title: 'To Do', boardId: board._id });
        await this.swimlaneService.create({ title: 'In Progress', boardId: board._id });
        await this.swimlaneService.create({ title: 'Done', boardId: board._id });
      }
    }

    // Populate swimlanes
    const swimlanes = await this.swimlaneService.findByBoardId(board._id);

    return {
      ...board.toObject(),
      swimlanes,
    };
  }
}
