import { Resolver, Query, Mutation, Args, ID } from '@nestjs/graphql';
import { UseGuards } from '@nestjs/common';
import { BoardService } from './board.service';
import { Board } from './schemas/board.schema';
import { CreateBoardInput } from './dto/create-board.input';
import { UpdateBoardInput } from './dto/update-board.input';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { CurrentUser } from '../auth/decorators/current-user.decorator';
import { User } from '../user/schemas/user.schema';

@Resolver(() => Board)
export class BoardResolver {
  constructor(private readonly boardService: BoardService) {}

  @Query(() => Board, { name: 'board' })
  async getBoard(
    @Args('boardId', { type: () => ID, nullable: true }) boardId?: string,
  ) {
    return this.boardService.getDefaultBoard();
  }

  @Query(() => Board, { name: 'userBoard' })
  @UseGuards(JwtAuthGuard)
  async getUserBoard(
    @CurrentUser() user: User,
    @Args('boardId', { type: () => ID, nullable: true }) boardId?: string,
  ) {
    return this.boardService.getUserBoard(user._id, boardId);
  }

  @Query(() => [Board], { name: 'userBoards' })
  @UseGuards(JwtAuthGuard)
  async getUserBoards(@CurrentUser() user: User) {
    return this.boardService.findByUserId(user._id);
  }

  @Mutation(() => Board)
  @UseGuards(JwtAuthGuard)
  async createBoard(
    @CurrentUser() user: User,
    @Args('createBoardInput') createBoardInput: CreateBoardInput,
  ) {
    return this.boardService.create({ ...createBoardInput, userId: user._id });
  }

  @Mutation(() => Board)
  @UseGuards(JwtAuthGuard)
  async updateBoard(
    @CurrentUser() user: User,
    @Args('updateBoardInput') updateBoardInput: UpdateBoardInput,
  ) {
    return this.boardService.update({ ...updateBoardInput, userId: user._id });
  }

  @Mutation(() => Board)
  @UseGuards(JwtAuthGuard)
  async deleteBoard(
    @CurrentUser() user: User,
    @Args('id', { type: () => ID }) id: string,
  ) {
    return this.boardService.remove(id, user._id);
  }
}
