#!/bin/bash

# Deployment script for cryptodo.dsserv.de server
echo "🚀 Deploying CryptoDo to cryptodo.dsserv.de server..."

# Stop existing containers
echo "📦 Stopping existing containers..."
docker compose -f docker-compose.prod.yml down

# Remove old images to ensure fresh build
echo "🧹 Cleaning up old images..."
docker image prune -f

# Build and start production containers
echo "🔨 Building and starting production containers..."
docker compose -f docker-compose.prod.yml up -d --build

# Show container status
echo "📊 Container status:"
docker compose -f docker-compose.prod.yml ps

# Show logs
echo "📝 Recent logs:"
docker compose -f docker-compose.prod.yml logs --tail=10

echo "✅ Deployment complete!"
echo "🌐 Frontend: https://cryptodo.dsserv.de"
echo "🔧 Backend: https://cryptodo.dsserv.de/graphql"

echo ""
echo "📋 Useful commands:"
echo "  View logs: docker compose -f docker-compose.prod.yml logs -f"
echo "  Stop services: docker compose -f docker-compose.prod.yml down"
echo "  Restart services: docker compose -f docker-compose.prod.yml restart"
